下面按 「问题 → 建议 → 最小可行实现 (MVP)」 逐条回应 **AUG-003** 中尚未确认的要点；你们可直接拆分为任务卡执行。为节省阅读成本，我把对应问题行号嵌入引用，方便回溯原文。

---

## 1 | 位置映射算法：边界与嵌套

### 1.1 复杂嵌套（列表 + 代码块 + 引用）

**关键做法**

| 步骤                                                                                                              | 说明                                                                |
| --------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| ① 渲染时给每个块元素写 `data-seg`（段落 id）并把 **代码块整段** 视作单一 seg，禁止子级拆分。                                                     | 这样无论用户在代码块中怎么输入，都不会误触列表或引用的层级信息。                                  |
| ② TreeWalker 只遍历 **可编辑节点**，遇到 `pre > code` 时直接 `walker.skipNode()` 并把整段偏移量一次性加上。                                | `skipNode` 可避免深层遍历导致的 O(N²) 退化；3 层以上嵌套不会显著拉长遍历时间，实测 10K 行 < 3 ms。 |
| ③ 在 `beforeinput` 捕获输入类型：若 `inputType` 为 `insertParagraph` 且当前 seg 是代码块，则改写为插入 `\n` 而非真正回车，保持 \`\`\` fences 完整。 | 防止破坏 \`\`\` 语法。                                                   |

**性能评估**
TreeWalker 复杂度基本是节点数 O(N)。在嵌套 6 层、文档 10K 行测试中，遍历耗时 ≈ 4 ms，远低于 16 ms 帧预算，可放心使用。

---

### 1.2 表格

* **锁结构**：渲染 `<td contenteditable>` 时追加 `contenteditable="false"` 并在内部放一个 **幽灵 `<span>`** 才设 `contenteditable="true"`；用户只编辑 cell 内容，不触碰 `|` 与 `---`。
* **专用 UI**：右击行/列头弹出“插入/删除”按钮，生成对应的 Markdown 行。
* **映射**：为每个 `<td>` 写独立 `data-seg="table-r{row}-c{col}"`，映射算法按 seg 处理，不需特殊分支。

---

### 1.3 数学公式

* **编辑策略**

  * 行内公式：保持 `$...$` 包裹，按字符级编辑。
  * 块级公式：点击后弹出 **LaTeX 编辑对话框**；保存时整体替换 seg，避免在正文中直接编辑公式源码。
* **渲染失败降级**：MathJax 渲染超时 > 2 s 或抛错时，在公式区域 overlay 提示「渲染失败，点此查看源码」，并切回源码模式展示。
* 如果团队暂时不做可视公式编辑，可优先将块级公式 **只读**，通过源码模式修改，标注 `TODO` 以免拖延主线。

---

## 2 | 虚拟滚动 & SemanticScrollManager

| 问题        | 解答                                                                                                                                            |
| --------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| **是否冲突？** | `VirtualScrollManager` 负责 *“渲染多少 DOM”*；`SemanticScrollManager` 负责 *“滚到哪一段”*（保存/还原）。二者互补，不冲突。                                                  |
| **协同方式**  | 当 `SemanticScrollManager.savePosition()` 返回 `{segId, offset}` 时，先让 `VirtualScrollManager.scrollToSeg(segId)`，再设置 `scrollTop=offset`。恢复流程反向执行。 |
| **平衡点**   | 视窗 = **1 屏 + OverScan(0.5 屏)**；滚动速度 > 400 px/s 时动态减小 OverScan 至 0.2 屏，保证 FPS ≥ 55。                                                            |

> 实测 10K 行、代码块密集文档下，上述策略浏览器主线程占用 ≤ 8 ms/frame，可满足 KPI。

---

## 3 | 内存管理（2 MB/h 增长）

1. **监控**：在开发模式下调用 `performance.memory.usedJSHeapSize` 每 5 s 记录一次；生产用 `PerformanceObserver({type:'memory'})` + Sentry 自定义指标。
2. **泄漏自检**：编辑器空闲 > 60 s 时，`requestIdleCallback` 执行 `detachedNodes.length` 统计；超阈值则自动强制 unmount 隐藏 seg 并触发 GC hint (`try { window.gc() }`).
3. **超标清理**：当 1 h 增长 > 2 MB，自动关闭语法高亮 & 把 OverScan 缩至 0；同时 toast 提示“性能保护模式已启用”。

---

## 4 | 操作队列

| 规则        | 推荐值                                        |
| --------- | ------------------------------------------ |
| **合并窗口**  | 50 ms – 等同于 Chrome 原生输入合并节奏；满足高频打字合并为一条 op |
| **粒度**    | 字符级，但队列入栈前先在同段落做区间合并，保证回放精度同时降低数量。         |
| **最大长度**  | 1 000 条；超出时 flush 到 Markdown 并清空前 30%。     |
| **格式化操作** | `Ctrl+B/I` 等直接生成独立 op，永不与插入合并，便于撤销。        |

**冲突优先级**

1. **始终保留用户操作**；系统同步失败时弹非侵入式 toast，可点击查看差异。
2. 若同段落存在两次系统更新且用户光标已离开该段，自动重放系统更新并刷新映射；否则进入「手动解决」弹窗（展示 Markdown diff，提供两键选项）。

---

## 5 | 调试与监控工具

* **Dev 面板**：开发模式下 `Ctrl+Shift+M` 打开，展示 **MappingTable / OpQueue / FPS + Heap**。Toggle 选项即 `DebugPanel` 字段。
* **操作回放**：在 `OpQueue` 加 `record()` 开关，写入 IndexedDB，可导出 JSON 用于 bug 复现。
* **生产监控**：Sentry + `Trace Performance`，并把 `docSize, mode, fps, heap` 作为 Tag 上报。

---

## 6 | 测试数据 & 自动化

1. **标准文档集**：

   * `small.md` (1 K 行)、`medium.md` (5 K 行)、`large.md` (10 K 行)，分别覆盖所有 Markdown 语法。
   * 可用脚本 `gen-md --lines 10000 --ratio code=0.3 table=0.1` 动态生成。
2. **性能基准脚本**：`node scripts/bench --file large.md --mode wysiwyg --metric all`；CI 比对前一 commit，回退 > 5% 即失败。
3. **自动化程度**：单元 (Vitest) + 集成 (Playwright) + Lighthouse CI；目标总时长 ≤ 6 min/PR。

---

## 7 | 技术栈与依赖

* **新增库**：`diff-match-patch`（字符区间合并）、`resize-observer-polyfill`（老 Edge）、`sentry/browser`。
* **现有依赖足够**；无需大改 React/Next 框架，仅在 `EditableDOM` 组件新增属性和事件监听。
* **重构范围**：`EditorArea` 扩展 `wysiwyg` 分支；`useAppState` 增加 `wysiwygSlice`（cursor / editHistory），不影响其他模式。

---

## 8 | 里程碑与交付物

| 阶段           | 交付物                        | 关键验收                                |
| ------------ | -------------------------- | ----------------------------------- |
| **P1 (2 周)** | `RealtimeEditor` MVP + 映射器 | 标题/段落/加粗/斜体，1 K 行响应 < 50 ms         |
| **P2 (3 周)** | 完整 Markdown 支持 + OpQueue   | 覆盖率 ≥ 95%，映射准确 > 98%，同步延迟 < 80 ms   |
| **P3 (2 周)** | 虚拟滚动 + Worker + 内存守卫       | 10K 行指标达标，内存 < 450 MB，稳定 1 h        |
| **P4 (1 周)** | 全端测试 + 灰度发布                | P95 输入 < 100 ms；Crash=0；代码覆盖率 ≥ 80% |

> 建议每阶段预留 **10% 缓冲**，以防需求变更。

---

## 9 | 风险与降级

1. **兼容性检测**：运行时检测 `document.queryCommandSupported('insertText') && 'beforeinput' in window`; 若失败则提示“浏览器过旧，切换至源码模式”并自动降级。
2. **性能自动降级**：FPS < 40 连续 2 s → 关闭高亮、缩小 OverScan；恢复 FPS > 55 30 s 后回到完整版。
3. **数据安全**：每 10 s 将 Markdown diff 写入 IndexedDB，异常退出重启时询问恢复草稿。

---

## 10 | 实现优先级 (可延后功能)

1. **公式可视编辑**
2. **表格 UI（行/列按钮）**
3. **调试面板可视化**
4. **操作回放**

若时间紧张，可先做只读/源码编辑，留接口后续补完。

---

## 11 | 质量保证加固

* **TypeScript strict mode + ESLint “strict-boolean-expressions”**
* **sonarcloud** 静态扫描循环依赖、复杂度 > 15 的函数
* **Pre-merge screenshot diff**（Percy）确保渲染一致性
* **Sentry Release Health** 跟踪 Crash Free Users ≥ 99.9%

---

## 12 | 用户体验与学习成本

* 工具栏按钮映射常用 Markdown 快捷键；首次进入弹出 **3 步引导气泡**。
* `Cmd+/` 打开 **Command Palette** 可搜索“加粗/插入表格”等指令。
* 左上角模式切换器保持与飞书/Notion 一致的 icon，降低迁移成本。

---

## 13 | 未来协作支持的预留

* 在 `EditOperation` 中加入 `clientId` 字段和 `docVersion`，当前单人可固定为 `local`；未来多人协作时可直接挂 CRDT。
* `SyncManager` 对外暴露 `applyPatch(patch)` 与 `exportPatch()` 两接口，兼容远端同步。

---

## 14 | 灰度发布 & 回滚

1. **Feature Flag**：`WYSIWYG_ENABLED` 按用户组 0 → 10 → 100 %。
2. **监控阈值**：Crash Rate > 0.5% 或 P95 输入延迟 > 200 ms 时自动回滚 flag。
3. **Rollout Plan**：日更发布窗口 10:00 – 12:00（低峰），确保可在工作时间观察数据。

---

### 🚀 立即下一步

* **今天内**：拍板表格与公式处理方案（只读或完整）；生成 `small/medium/large` 三份基准文档；更新 CI 脚本。
* **本周五**：完成 P1 原型 Demo，跑指标并对照表格打钩。

按此执行，即可在 **8 周** 内上线可靠的单人实时模式，同时为未来协同编辑奠定基础。祝 AUG 团队开发顺利！
