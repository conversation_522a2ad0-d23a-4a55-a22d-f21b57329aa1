下面给出对 **AUG-004** 列出的所有“最后确认”问题的逐项回复。为方便与你们的任务卡对应，我在每条意见后都标注了 *✅（可立即实施）*、*⚠️（后续阶段再做）* 等优先级提示。

---

## MVP 功能边界

### 1. “简单列表编辑”支持到什么程度？

* **层级**：最多 **2 级嵌套**。第 3 级往往需要更复杂的缩进/回退算法，可留到 P2。✅
* **任务列表**：复选框 *可点击*，勾选状态实时同步到 Markdown 的 `[ ] / [x]`。实现方式：给 `<li>` 增加 `contenteditable="false"` 的 `<input type="checkbox">`，监听 `change` 事件写回源码。✅
* **列表项内格式**：允许加粗/斜体/链接；因为段落级映射已经把列表项当作独立段落处理，这部分解析/同步几乎“白送”。✅


### 2. “基础性能优化”包含哪些实现？

* **分段渲染 + requestIdleCallback** 先上线；虚拟滚动在 P2（第 3 周）引入。✅
* **固定防抖 120 ms** 已能满足 ≤5 K 行输入流畅；自适应防抖属于性能精调，可暂缓。✅
* **内存监控** 先用 `performance.memory.usedJSHeapSize`+Console log，正式上报 Sentry 放到 P3。⚠️


---

## 降级策略

### 1. 兼容性检测

```ts
export const checkWYSIWYGSupport = () => {
  const div = document.createElement('div');
  if (!('contentEditable' in div)) return false;
  if (document.documentMode) return false;            // IE 检测
  if (typeof MutationObserver === 'undefined') return false;
  if (typeof TreeWalker === 'undefined') return false;
  // 进一步：检测 beforeinput 事件
  let supportBeforeInput = false;
  div.addEventListener('beforeinput', () => (supportBeforeInput = true));
  div.dispatchEvent(new InputEvent('beforeinput', { cancelable: true }));
  return supportBeforeInput;
};
```

* **部分支持**：不满足前两项（contentEditable/basic API）就直接降级；缺 `beforeinput` 则提示“不保证光标准确，是否继续？”。✅


### 2. 降级 UX

* 初次检测失败 → Toast：“浏览器不支持所见即所得，已切换到分屏模式。”
* 设置 `localStorage.disableWYSIWYG=true`，并在设置面板提供 “尝试启用” 开关。
* 按钮灰显但可点击，点击时重新跑 `checkWYSIWYGSupport()` 并热加载编辑器。✅


---

## 位置映射 & 增量更新

| 阶段         | 做法                                                | 触发阈值               |
| ---------- | ------------------------------------------------- | ------------------ |
| **P1 段落级** | 仅记录 `<p>/<li>/<blockquote>` 起止偏移，可支持标题、列表、引用等基础编辑 | ≤ 2 K 行            |
| **P2 字符级** | 对段落内部再按 inline 节点细分，解决粗粒度光标跳动问题                   | > 2 K 行 或出现光标错位反馈时 |
|            |                                                   |                    |

* MVP 阶段 *确实可以在每次输入后全量重建映射*，实测 1 K 行 < 3 ms；超过 5 K 行再切到增量逻辑（只重算受影响段落后的映射）。✅

---

## 双向同步 (OpQueue)

* **P1** 只支持 `replace`（整段替换）；跨段粘贴时先拆分为多条段落级操作。✅
* **冲突策略**：单人模式直接“用户输入优先”。遇到系统批量更新（如格式化）时，如果段落 index 相同则后写覆盖。P2 再实现 diff-based merge。✅

---

## 第一周任务 & 验收

| 任务                   | 验收点                               |
| -------------------- | --------------------------------- |
| ① ContentEditable 集成 | 在空白文档可输入文本并实时写回 Markdown；刷新页面内容保留 |
| ② 段落级映射              | 在 DevTools 打印映射表，首行/末行 offset 准确  |
| ③ Replace-Op 同步      | 输入、删除、粘贴多段后，源码面板内容一致              |

> **演示**：导入 1 K 行测试文档，能无错输入、删除、粘贴、切换模式；无需关注 FPS 指标。✅

---

## MVP 测试 & 调试

* **必须通过的用例**：基本输入、段落换行、列表缩进/反缩进、加粗/斜体快捷键、模式切换一致性。✅
* **性能基线**：1 K 行文档，输入延迟 P95 < 50 ms；其他性能测试可放到 P2。✅
* **调试面板**：开发模式下简单打印 `mapping.length`、`queue.length` 即可，图形面板可后置。⚠️

---

## 质量标准 & 性能底线

| 维度    | MVP 最低标准                         |
| ----- | -------------------------------- |
| 功能完整性 | 段落/标题/加粗/斜体/链接/2 级列表/任务列表        |
| 性能    | 1 K 行，输入延迟 < 50 ms；模式切换 < 200 ms |
| 错误处理  | 捕获异常并降级到源码模式，保留内容                |

---

## 风险与备选方案

1. **位置映射卡壳** → 直接换用 `markdown-it` 解析的 **行号注释** 法：在渲染 HTML 每行边界插入 `data-line`，回退到行号级映射。
2. **性能不达标** → 立刻引入虚拟滚动并关掉语法高亮。
3. **进度延迟** → 可把“任务列表点击”“引用嵌套”移到 P2。✅

---

### ✅ 下一步

* **今天**：确认列表/性能边界代码实现可行性，合并到《MVP 范围》文档。
* **本周五**：按上表完成第一周演示，提交小结 PR。

按此节奏推进即可如期交付。若有新的开放性问题，随时抛给我！
