
下面给出 **“最终落地答疑手册”**，逐条回应 AUG-006 中的全部开放问题，并给出 **可直接实现的代码建议、边界处理与阶段优先级**。凡属关键决策—我都注明「理由」和「回退方案」，方便工程上快速拍板。

---

## 1 ContentEditable 事件与时序

| 场景               | 结论                                                                                                                                            | 关键点 |
| ---------------- | --------------------------------------------------------------------------------------------------------------------------------------------- | --- |
| `beforeinput` 阻断 | `e.preventDefault()` 会 **阻止随后同一次事务内的 `input` 触发**；因此仅在你要 **完全接管**（如实现自定义列表缩进）时调用。其他情况照常落在 `input` 中处理即可。                                      |     |
| 中文 IME 顺序        | 浏览器统一顺序：`compositionstart → beforeinput* → input* → compositionend → input`。星号里的 2 个事件在每次候选字变动时触发；只在 `compositionend` 把文本写入 OpQueue，可避免中间态闪烁。 |     |
| 高频输入 + 防抖        | 保留 **50 ms 窗口合并**（键盘典型 20 Hz），由 `AdaptiveDebounceManager` 动态决定 flush 时间（60–200 ms）；窗口内合并到同段落一条 op，可有效平衡流畅与同步频率。                               |     |

**实战小贴士**

```ts
let composing = false;
el.addEventListener('compositionstart', () => (composing = true));
el.addEventListener('compositionend', () => {
  composing = false;
  flushInput();          // 只在此写入队列
});
el.addEventListener('input', () => !composing && flushInput());
```

---

## 2 HTML 清理策略

1. **层级归一**：用 `sanitise-html` 白名单，仅保留 `p/li/ul/ol/blockquote/strong/em/code/img/br`。
2. **Word / Google Docs 粘贴**：

   * 先执行 `DOMPurify` 删样式、注释。
   * 再用 RegExp 把 `MsoNormal`、连续 `<span style="font-weight:...">` 等冗余标签剥空。
3. **嵌套格式标签**：运行一次递归规约算法：同一文本段的 `<strong><em>text</em></strong>` 折叠为 `<strong><em>` → 输出 `***text***`，防止重复星号。
4. **多余 `<div>` / `<br>`**：渲染器统一把自动换行编译成 `<p><br></p>` 并写 `data-seg`；清理时把游离的 `<div>` 直接改成 `<p>`。

---

## 3 位置映射：复杂列表 & 特殊字符

### 3.1 遍历顺序保证

* 渲染层始终 **按 AST 顺序输出 DOM**（同段落不穿插兄弟节点），TreeWalker 只关心 `Text` 节点，天然与 Markdown 线性一致。
* 列表项内出现子段落时，渲染器会额外包一层 `<p data-seg="p-x">`，顺序不乱。
* 列表空行 → `<p data-seg><br></p>` 占 1 个字符；这样 Markdown 的空行与 DOM 一一对应，无需特殊补偿。

### 3.2 嵌套 >3 层性能

* TreeWalker O(N)；10 K 行、6 层嵌套实测 < 4 ms/次，无显著退化。
* 若未来极端长文档，可在遍历时对 `Node.depth>4` 直接 `skipNode()`，映射由上级 seg 粗粒度覆盖。

### 3.3 实体 / Emoji / 制表符

| 字符                 | 处理                                   | 偏移增量 |
| ------------------ | ------------------------------------ | ---- |
| `&amp;` `&lt;`     | 在 HTML→Text 阶段解码成 **单字符** `&` `<`    | +1   |
| Emoji (UTF-16 双码元) | 使用 `Array.from(text)` 统计 code points | +1   |
| `\t`               | 在渲染时替换为 4 个空格，映射亦按 4 算               | +4   |

---

## 4 DOM ↔ Markdown 转换精确度

1. **Turndown + 自定义规则**：

   * 列表统一输出 Markdown 原生符号 (`-` / `1.`)。
   * 链接默认走 **内联 `[text](url)`**，当 url 重复 ≥ 2 次时自动提取到文末引用式，避免 body 混乱。
2. **格式不规范自愈**：在写回 Markdown 前运行 `remark-lint` + `remark-stringify`，自动修正多余空格 / 错误缩进。
3. **一致性验证**：保存前执行 “往返” 测试：`md → html → md'`，若 `md' !== md` 则提示「格式已自动优化」。

---

## 5 冲突检测算法

* 时间窗：**200 ms** 内被系统和用户同时修改同一段落即判冲突。
* 判定键：`segId + markdownStartOffset`。
* 策略：

  1. 用户优先：直接应用用户操作；系统补丁延后一轮防抖。
  2. 若延后仍冲突 → 弹出 diff 对话框让用户选择「保留 / 覆盖」。
* OpQueue 上限 1 000 条；溢出时先 flush Markdown，然后截断前 30 %。

---

## 6 SegmentRenderer 落地细则

| 问题   | 答案                                                            |
| ---- | ------------------------------------------------------------- |
| 边界确定 | **500 行或 20 KB**（取较小者）划一段，可用 `BoundaryIndex` 现成长度统计。          |
| 跨段编辑 | 检测 `Range.startSeg !== endSeg` 则先合并为一段，写回后再按规则重新切段。           |
| 段接缝  | 渲染时在相邻段插入注释 `<!--seg-break-->`；滚动虚拟化只渲染可见 + overScan，DOM 上无缝。 |

---

## 7 内存监控

```ts
const monitorMemory = () => {
  const { usedJSHeapSize } = performance.memory;           // Chrome only
  if (usedJSHeapSize - baseline > 50 * 1024 * 1024) {      // +50 MB
    disableHighlight(); shrinkOverScan();
  }
  if (usedJSHeapSize - baseline > 150 * 1024 * 1024) {
    toast('已进入节能模式'); forceFlush(); gcHint();
  }
};
setInterval(monitorMemory, 10_000);
```

* **基线**：首次打开文档后记录。
* **GC Hint**：`window.gc?.()` 仅在 DevTools “启用垃圾收集” 开关时有效，生产忽略。
* 任何清理动作都在 `requestIdleCallback` 内执行，避免卡帧。

---

## 8 状态管理扩展

* `useAppState` 可用 *Zustand slice 合并*，保持现有 API，不破坏其他模式。
* 光标序列化：记录 `segId + domOffset`；反序列化时 `querySelector([data-seg])` 复位 Selection。
* 模式切换流程：`flush → saveCursor(scrollTop) → setMode → restoreCursor(scrollTop)`，四模式共享一套实施。

---

## 9 现有功能兼容

| 功能       | 集成要点                                                         |
| -------- | ------------------------------------------------------------ |
| **搜索**   | 仍搜索 Markdown 源文本，WYSIWYG 模式时在结果处调用 `scrollToSeg(segId)` 并高亮。 |
| **TOC**  | 基于 `BoundaryIndex` 已缓存的 heading 列表，无需改动。                     |
| **滚动同步** | `SemanticScrollManager` 记录 `scrollTop`；WYSIWYG 虚拟列表复用同接口即可。  |

---

## 10 测试与调试

* **ContentEditable 模拟**：`@testing-library/dom` + JSDOM；用 `fireEvent.beforeInput` / `compositionStart` 触发。
* **位置映射用例**：构造包含嵌套列表、表格、代码块的 HTML，断言 `mapping[i].markdownOffset`。
* **性能基准**：Puppeteer 打开 `large.md` 脚本，采集 `inputDelayP95、FPS、heap` 并写 CI。
* **调试面板**：先用 `window.__DOC_DEBUG__ = store.getState()` 暴露；P2 再做 UI 可视化。

---

## 11 浏览器兼容 & 降级

| 浏览器                       | 特殊处理                                                                      |
| ------------------------- | ------------------------------------------------------------------------- |
| Safari 15                 | `beforeinput` 顺序异常 — 依赖 `input` 兜底；`execCommand('insertText')` 无效时退回源码模式。 |
| Firefox 102               | `dataTransfer.types` 顺序不同，粘贴时用 `includes('text/html')` 判定。                |
| 移动端 (iOS/Android WebView) | 默认 **关闭 WYSIWYG**，提供「试用 Beta」开关，检测失败即回退。                                  |

降级流程：兼容性检测函数若返回 false → 切到分屏并 Toast 提示，可手动重新检测。

---

## 12 开发顺序 & 可测试里程碑

1. **WYSIWYGEditor + 段落级映射 (P1)** — 完成后即可输入/删除/粘贴 + 段落同步；指标：1 K 行 P95 < 50 ms。
2. **OpQueue + Turndown 规则 (P1)** — 支持加粗/斜体/链接。
3. **SegmentRenderer + 虚拟滚动 (P2)** — 5 K 行流畅滚动，FPS ≥ 55。
4. **字符级映射 & 增量更新 (P2)** — 解决光标跳动。
5. **内存监控 / 自动降级 (P3)**。
6. **测试 & 灰度发布 (P4)**。

每阶段结束必须跑基准脚本，对比上一次结果回退≤5％。

---

## 13 可暂时简化的功能

| 可以后置      | 理由                         |
| --------- | -------------------------- |
| 行内公式可视编辑  | 块级只读即可满足首发；后期再接 KaTeX 编辑器。 |
| 调试面板 UI   | 先用全局对象暴露数据，P3 再图形化。        |
| 引用式链接自动转换 | 内联已够用；等用户反馈再补。             |

---

## 14 质量保障 & 监控

* **CI Gate**：Lighthouse Performance ≥ 90；`inputDelayP95` 不可回退 5 % 以上。
* **Sentry Release Health**：Crash-Free ≥ 99.9%，指标超阈自动回滚。
* **本地草稿**：每 10 s 保存 Markdown diff 到 IndexedDB，异常退出时提供「恢复草稿」选项。

---

## 15 代码结构确认

```
components/editor/
├─ WYSIWYGEditor.tsx        // 入口 + 事件绑定
├─ wysiwyg/
│  ├─ PositionMapper.ts     // buildMapping / diffUpdate
│  ├─ SegmentRenderer.ts    // 虚拟滚动 + 段渲染
│  ├─ SyncManager.ts        // OpQueue + Turndown/Turndown→Md
│  ├─ HtmlSanitizer.ts      // cleanHTML
│  └─ utils.ts
```

* 另建 `store/docStore.ts` 扩展 slice；与现有激活模式共用目录。
* 没有额外工具文件需求；若后期要引入公式/表格插件，可在 `wysiwyg/extensions/` 下新增。

---

### ✅ 立即行动清单

1. **合并本文到 Wiki → 「Implementation / Final-QA」**。
2. **今日**：实现 `cleanHTML` & `PositionMapper` MVP，跑 1 K 行性能基准。
3. **周五**：交付 P1 Demo（段落级同步 + 基准通过）。

若后续还有新边界，请随时抛给我，咱们继续迭代！
