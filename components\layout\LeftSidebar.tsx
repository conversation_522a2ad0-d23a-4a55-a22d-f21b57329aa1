'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { CreateFileDialog } from '@/components/ui/create-file-dialog';
import {
  List,
  FileText,
  GitBranch,
  History,
  Search,
  ChevronRight,
  ChevronDown,
  File,
  Folder,
  FolderOpen,
  Trash2,
  Plus,
  Edit,
  RotateCw,
} from 'lucide-react';
import { GitPanel } from '@/components/git/GitPanel';
import { HistoryPanel } from '@/components/git/HistoryPanel';

interface LeftSidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  currentFileContent?: string; // 添加当前文件内容属性
  gitStatusVersion?: number; // 添加Git状态版本控制
  onCommitContentChange?: (content: string, filename: string) => void;
  activeTab?: string; // 当前活跃的标签页
  onActiveTabChange?: (tab: string) => void; // 标签页变化回调
}

export function LeftSidebar({
  isCollapsed,
  onToggle,
  onFileSelect,
  currentFile,
  currentFileContent,
  gitStatusVersion,
  onCommitContentChange,
  activeTab: externalActiveTab,
  onActiveTabChange
}: LeftSidebarProps) {
  // 使用外部传入的activeTab，如果没有则使用本地状态
  const [localActiveTab, setLocalActiveTab] = useState('outline');
  const activeTab = externalActiveTab || localActiveTab;

  // 处理标签页切换
  const handleTabChange = (tab: string) => {
    if (onActiveTabChange) {
      onActiveTabChange(tab);
    } else {
      setLocalActiveTab(tab);
    }
  };

  const sidebarTabs = [
    { id: 'outline', icon: List, label: '大纲', tooltip: '查看文档大纲结构' },
    { id: 'files', icon: FileText, label: '文件', tooltip: '浏览项目文件' },
    { id: 'git', icon: GitBranch, label: 'Git', tooltip: '查看Git状态和提交历史' },
    { id: 'history', icon: History, label: '历史', tooltip: '查看编辑历史记录' },
    { id: 'search', icon: Search, label: '搜索', tooltip: '在文件中搜索内容' },
  ];

  return (
    <TooltipProvider>
      <div className="flex h-full bg-secondary/30">
        {/* Icon Bar */}
        <div className="w-12 bg-secondary border-r border-border flex flex-col items-center py-2 space-y-1">
          {sidebarTabs.map((tab) => (
            <Tooltip key={tab.id}>
              <TooltipTrigger asChild>
                <Button
                  variant={activeTab === tab.id ? 'default' : 'ghost'}
                  size="sm"
                  className="w-8 h-8 p-0"
                  onClick={() => handleTabChange(tab.id)}
                >
                  <tab.icon className="w-4 h-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right" sideOffset={8}>
                <p className="text-xs">{tab.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Content Panel */}
        {!isCollapsed && (
          <div className="w-64 bg-background border-r border-border flex flex-col">
            {/* Header */}
            <div className="h-8 flex items-center justify-between px-3 bg-secondary/50">
              <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                {sidebarTabs.find((tab) => tab.id === activeTab)?.label}
              </span>
            </div>

            <Separator />

            <ScrollArea className="flex-1">
              <div className="p-2">
                {activeTab === 'outline' && (
                  <OutlinePanel 
                    content={currentFileContent || ''} 
                    currentFile={currentFile}
                  />
                )}
                {activeTab === 'files' && <FilesPanel onFileSelect={onFileSelect} currentFile={currentFile} gitStatusVersion={gitStatusVersion} />}
                {activeTab === 'git' && (
                  <GitPanel
                    onFileSelect={onFileSelect}
                    currentFile={currentFile}
                    currentFileContent={currentFileContent}
                    gitStatusVersion={gitStatusVersion}
                  />
                )}
                {activeTab === 'history' && (
                  <HistoryPanel
                    onFileSelect={onFileSelect}
                    currentFile={currentFile}
                    onCommitContentChange={onCommitContentChange}
                  />
                )}
                {activeTab === 'search' && <SearchPanel />}
              </div>
            </ScrollArea>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}

// 大纲面板组件
interface OutlinePanelProps {
  content: string;
  currentFile?: string | null;
}

interface HeadingItem {
  id: string;
  text: string;
  level: number;
  line: number;
}

function OutlinePanel({ content, currentFile }: OutlinePanelProps) {
  const [expandedLevels, setExpandedLevels] = useState<Set<number>>(new Set([1, 2]));
  const [activeHeading, setActiveHeading] = useState<string>('');

  // 解析Markdown内容，提取标题
  const parseHeadings = (markdown: string): HeadingItem[] => {
    if (!markdown) return [];

    const lines = markdown.split('\n');
    const headings: HeadingItem[] = [];
    
    lines.forEach((line, index) => {
      // 匹配 ATX 标题格式 (# ## ### 等)
      const atxMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (atxMatch) {
        const level = atxMatch[1].length;
        const text = atxMatch[2].trim();
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '') // 保留中文、英文、数字、空格、连字符
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        headings.push({
          id: id || `heading-${index}`,
          text,
          level,
          line: index + 1
        });
      }
    });

    return headings;
  };

  const headings = parseHeadings(content);

  // 切换展开状态
  const toggleLevel = (level: number) => {
    const newExpanded = new Set(expandedLevels);
    if (newExpanded.has(level)) {
      newExpanded.delete(level);
    } else {
      newExpanded.add(level);
    }
    setExpandedLevels(newExpanded);
  };

  // 检查某级别是否应该显示
  const shouldShowLevel = (level: number): boolean => {
    if (level === 1) return true;
    for (let i = 1; i < level; i++) {
      if (!expandedLevels.has(i)) return false;
    }
    return true;
  };

  // 检测当前编辑模式
  const getCurrentEditorMode = (): string => {
    // 检查页面中的编辑器模式标识
    const tabsTrigger = document.querySelector('[data-state="active"][role="tab"]');
    if (tabsTrigger) {
      const tabValue = tabsTrigger.getAttribute('data-value') || 
                     tabsTrigger.textContent?.trim().toLowerCase();
      
      if (tabValue?.includes('源码') || tabValue === 'source') return 'source';
      if (tabValue?.includes('预览') || tabValue === 'preview') return 'preview';
      if (tabValue?.includes('分屏') || tabValue === 'split') return 'split';
    }
    
    // 备用检测方法：通过DOM结构判断
    if (document.querySelector('.cm-editor')) {
      if (document.querySelector('.prose')) return 'split';
      return 'source';
    }
    if (document.querySelector('.prose') && !document.querySelector('.cm-editor')) {
      return 'preview';
    }

    
    return 'source'; // 默认源码模式
  };

  // 跳转到对应标题位置
  const scrollToHeading = (headingId: string, line: number) => {
    console.log(`跳转到标题: ${headingId}, 第 ${line} 行`);
    
    const currentMode = getCurrentEditorMode();
    console.log(`当前编辑模式: ${currentMode}`);
    
    switch (currentMode) {
      case 'source':
        // 源码模式：跳转到CodeMirror指定行
        const sourceEvent = new CustomEvent('scrollToLine', {
          detail: { line: line }
        });
        document.dispatchEvent(sourceEvent);
        break;
        
      case 'preview':
        // 预览模式：滚动到HTML标题元素
        const previewElement = document.querySelector(`#${headingId}`);
        if (previewElement) {
          previewElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
          console.warn(`找不到预览元素: #${headingId}`);
          // 备用方案：尝试通过标题文本查找
          const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
          for (let i = 0; i < headingElements.length; i++) {
            const element = headingElements[i];
            if (element.textContent?.toLowerCase().includes(headingId.toLowerCase())) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              break;
            }
          }
        }
        break;
        
      case 'split':
        // 分屏模式：同时在源码和预览侧跳转
        // 跳转源码侧
        const splitSourceEvent = new CustomEvent('scrollToLine', {
          detail: { line: line }
        });
        document.dispatchEvent(splitSourceEvent);
        
        // 跳转预览侧
        setTimeout(() => {
          const splitPreviewElement = document.querySelector(`#${headingId}`);
          if (splitPreviewElement) {
            splitPreviewElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }, 100);
        break;
        

        
      default:
        console.warn(`未知的编辑模式: ${currentMode}`);
        // 默认尝试源码模式跳转
        const defaultEvent = new CustomEvent('scrollToLine', {
          detail: { line: line }
        });
        document.dispatchEvent(defaultEvent);
    }
  };

  if (!currentFile) {
    return (
      <div className="space-y-2 text-center py-8">
        <div className="text-xs text-muted-foreground">
          选择一个文件以查看大纲
        </div>
      </div>
    );
  }

  if (headings.length === 0) {
    return (
      <div className="space-y-2">
        <div className="text-xs text-muted-foreground mb-3">文档大纲</div>
        <div className="text-xs text-muted-foreground text-center py-4">
          当前文档没有标题
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-1 outline-panel">
      <div className="text-xs text-muted-foreground mb-3">文档大纲</div>
      
      {/* 统计信息 */}
      <div className="text-xs text-muted-foreground mb-2 px-2">
        共 {headings.length} 个标题
      </div>

      {/* 标题列表 */}
      <div className="space-y-0.5">
        {headings.map((heading, index) => {
          const isVisible = shouldShowLevel(heading.level);
          const hasChildren = index < headings.length - 1 && headings[index + 1].level > heading.level;
          const isExpanded = expandedLevels.has(heading.level);
          const isActive = activeHeading === heading.id;
          
          if (!isVisible) return null;

          return (
            <div 
              key={`${heading.id}-${index}`} 
              className={`group outline-item ${isActive ? 'active' : ''}`}
              data-level={heading.level}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  scrollToHeading(heading.id, heading.line);
                  setActiveHeading(heading.id);
                }}
                className="w-full justify-start text-xs font-normal h-6 px-2 py-1 hover:bg-muted/80 text-left"
                style={{ 
                  paddingLeft: `${(heading.level - 1) * 12 + 8}px`,
                  minHeight: '24px'
                }}
              >
                <div className="flex items-center space-x-1.5 min-w-0 flex-1">
                  {/* 展开/折叠按钮 */}
                  {hasChildren && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleLevel(heading.level);
                      }}
                      className="flex-shrink-0 p-0 hover:bg-muted-foreground/20 rounded outline-expand-icon"
                    >
                      {isExpanded ? (
                        <ChevronDown className="w-3 h-3" />
                      ) : (
                        <ChevronRight className="w-3 h-3" />
                      )}
                    </button>
                  )}
                  
                  {/* 级别标识 */}
                  <span className="text-xs font-mono text-muted-foreground w-6 flex-shrink-0">
                    H{heading.level}
                  </span>
                  
                  {/* 标题文本 */}
                  <span 
                    className="truncate flex-1 text-left" 
                    title={heading.text}
                    style={{
                      fontSize: `${Math.max(11, 14 - heading.level)}px`,
                      fontWeight: heading.level <= 2 ? '600' : heading.level <= 3 ? '500' : '400'
                    }}
                  >
                    {heading.text}
                  </span>
                  
                  {/* 行号显示 */}
                  <span className="text-xs text-muted-foreground/60 opacity-0 group-hover:opacity-100 transition-opacity">
                    {heading.line}
                  </span>
                </div>
              </Button>
            </div>
          );
        })}
      </div>
      
      {/* 快捷操作 */}
      <div className="pt-2 border-t border-border/50 mt-3">
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpandedLevels(new Set([1, 2, 3, 4, 5, 6]))}
            className="h-5 text-xs px-1 py-0"
          >
            全部展开
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpandedLevels(new Set([1]))}
            className="h-5 text-xs px-1 py-0"
          >
            折叠
          </Button>
        </div>
      </div>
    </div>
  );
}

interface FilesPanelProps {
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  gitStatusVersion?: number;
}

interface FileTreeNode {
  name: string;
  path: string;
  type: 'file' | 'folder';
  children?: FileTreeNode[];
  isExpanded?: boolean;
  size?: number;
  gitStatus?: 'modified' | 'added' | 'deleted' | 'untracked' | 'staged';
}

function FilesPanel({ onFileSelect, currentFile, gitStatusVersion }: FilesPanelProps) {
  const [expandedFolders, setExpandedFolders] = useState<string[]>(() => {
    // 从localStorage恢复展开状态
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('files-panel-expanded');
      if (saved) {
        try {
          return JSON.parse(saved);
        } catch {
          return [];
        }
      }
    }
    return [];
  });

  const [fileTree, setFileTree] = useState<FileTreeNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [showHiddenFiles, setShowHiddenFiles] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'type' | 'modified'>('type');
  const [contextMenu, setContextMenu] = useState<{
    x: number;
    y: number;
    filePath: string;
    fileType: 'file' | 'folder';
  } | null>(null);
  
  // 新建文件/文件夹弹窗状态
  const [createDialog, setCreateDialog] = useState<{
    open: boolean;
    type: 'file' | 'folder';
    folderPath?: string;
  }>({
    open: false,
    type: 'file',
    folderPath: undefined
  });

  // 构建文件树结构
  const buildFileTree = async (): Promise<FileTreeNode[]> => {
    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      const files = gitService.getFileList();
      const fileStatuses = gitService.getFileStatus();
      
      // 创建状态映射
      const statusMap = new Map<string, string>();
      fileStatuses.forEach(status => {
        if (status.staged) {
          statusMap.set(status.filepath, 'staged');
        } else {
          statusMap.set(status.filepath, status.status);
        }
      });

      // 构建树结构
      const tree: Map<string, FileTreeNode> = new Map();
      const rootNodes: FileTreeNode[] = [];

      files.forEach(filePath => {
        // 跳过隐藏文件（如果设置了）
        if (!showHiddenFiles && filePath.split('/').some(part => part.startsWith('.'))) {
          return;
        }

        const parts = filePath.split('/');
        let currentPath = '';
        let parentNode: FileTreeNode | null = null;

        parts.forEach((part, index) => {
          currentPath = currentPath ? `${currentPath}/${part}` : part;
          const isFile = index === parts.length - 1 && part.includes('.');
          
          if (!tree.has(currentPath)) {
            const node: FileTreeNode = {
              name: part,
              path: currentPath,
              type: isFile ? 'file' : 'folder',
              children: isFile ? undefined : [],
              isExpanded: expandedFolders.includes(currentPath),
              gitStatus: statusMap.get(currentPath) as any
            };

            tree.set(currentPath, node);

            if (parentNode) {
              parentNode.children!.push(node);
            } else {
              rootNodes.push(node);
            }
          }

          parentNode = tree.get(currentPath)!;
        });
      });

      // 排序
      const sortNodes = (nodes: FileTreeNode[]): FileTreeNode[] => {
        return nodes.sort((a, b) => {
          if (sortBy === 'type') {
            if (a.type !== b.type) {
              return a.type === 'folder' ? -1 : 1;
            }
          }
          return a.name.localeCompare(b.name);
        });
      };

      const sortTree = (nodes: FileTreeNode[]): FileTreeNode[] => {
        const sorted = sortNodes(nodes);
        sorted.forEach(node => {
          if (node.children) {
            node.children = sortTree(node.children);
          }
        });
        return sorted;
      };

      return sortTree(rootNodes);
    } catch (error) {
      console.error('构建文件树失败:', error);
      return [];
    }
  };

  // 加载文件树
  const loadFileTree = async () => {
    setLoading(true);
    try {
      const tree = await buildFileTree();
      setFileTree(tree);
    } finally {
      setLoading(false);
    }
  };

  // 初始化和Git状态变化时重新加载
  useEffect(() => {
    loadFileTree();
  }, [gitStatusVersion, showHiddenFiles, sortBy]);

  // 切换文件夹展开状态
  const toggleFolder = (folderPath: string) => {
    setExpandedFolders((prev) => {
      const newExpanded = prev.includes(folderPath)
        ? prev.filter((f) => f !== folderPath)
        : [...prev, folderPath];

      // 保存到localStorage
      localStorage.setItem('files-panel-expanded', JSON.stringify(newExpanded));
      return newExpanded;
    });
  };

  // 获取文件图标
  const getFileIcon = (fileName: string, type: 'file' | 'folder', isExpanded?: boolean) => {
    if (type === 'folder') {
      return isExpanded ? <FolderOpen className="w-3 h-3" /> : <Folder className="w-3 h-3" />;
    }
    
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'md':
        return <FileText className="w-3 h-3" />;
      case 'txt':
        return <File className="w-3 h-3" />;
      default:
        return <File className="w-3 h-3" />;
    }
  };

  // 获取状态指示器
  const getStatusIndicator = (status?: string) => {
    if (!status) return null;
    
    const statusConfig = {
      modified: { color: 'text-orange-500', text: 'M' },
      added: { color: 'text-green-500', text: 'A' },
      deleted: { color: 'text-red-500', text: 'D' },
      untracked: { color: 'text-blue-500', text: '?' },
      staged: { color: 'text-green-600', text: 'S' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    return (
      <span className={`text-xs font-mono font-bold ${config.color} w-3 text-center`}>
        {config.text}
      </span>
    );
  };

  // 渲染文件树节点
  const renderTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isCurrentFile = currentFile === node.path;
    const paddingLeft = level * 12 + 8;

    return (
      <div key={node.path}>
        <Button
          variant={isCurrentFile ? 'secondary' : 'ghost'}
          size="sm"
          className={`w-full justify-start text-xs font-normal h-6 px-2 relative group ${
            isCurrentFile ? 'bg-secondary' : 'hover:bg-muted/80'
          }`}
          style={{ paddingLeft: `${paddingLeft}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path);
            } else if (onFileSelect) {
              onFileSelect(node.path);
            }
          }}
          onContextMenu={(e) => {
            e.preventDefault();
            setContextMenu({
              x: e.clientX,
              y: e.clientY,
              filePath: node.path,
              fileType: node.type
            });
          }}
        >
          <div className="flex items-center space-x-1.5 min-w-0 flex-1">
            {/* 文件夹展开图标 */}
            {node.type === 'folder' && (
              <span className="flex-shrink-0">
                {expandedFolders.includes(node.path) ? (
                  <ChevronDown className="w-3 h-3" />
                ) : (
                  <ChevronRight className="w-3 h-3" />
                )}
              </span>
            )}
            
            {/* 文件/文件夹图标 */}
            <span className="flex-shrink-0">
              {getFileIcon(node.name, node.type, expandedFolders.includes(node.path))}
            </span>
            
            {/* 文件名 */}
            <span className="truncate flex-1 text-left" title={node.path}>
              {node.name}
            </span>
            
            {/* Git状态指示器 */}
            {getStatusIndicator(node.gitStatus)}
            
            {/* 重命名按钮 - 悬停时显示 */}
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-blue-500/20 rounded text-muted-foreground hover:text-blue-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    renameFileOrFolder(node.path, node.type);
                  }}
                >
                  <Edit className="w-3 h-3" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">重命名{node.type === 'folder' ? '文件夹' : '文件'}</p>
              </TooltipContent>
            </Tooltip>
            
            {/* 文件夹创建文件按钮 - 仅文件夹显示 */}
            {node.type === 'folder' && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <button
                    className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-green-500/20 rounded text-muted-foreground hover:text-green-600"
                                      onClick={(e) => {
                    e.stopPropagation();
                    openCreateFileDialog('file', node.path);
                  }}
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">在此文件夹中新建文件</p>
                </TooltipContent>
              </Tooltip>
            )}
            
            {/* 删除按钮 - 悬停时显示 */}
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-destructive/20 rounded text-muted-foreground hover:text-destructive"
                  onClick={(e) => {
                    e.stopPropagation();
                    deleteFileOrFolder(node.path, node.type);
                  }}
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">删除{node.type === 'folder' ? '文件夹' : '文件'}</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </Button>

        {/* 渲染子节点 */}
        {node.type === 'folder' && 
         node.children && 
         expandedFolders.includes(node.path) && (
          <div>
            {node.children.map(child => renderTreeNode(child, level + 1))}
          </div>
              )}

      {/* 创建文件/文件夹弹窗 */}
      <CreateFileDialog
        open={createDialog.open}
        onOpenChange={(open) => setCreateDialog({ ...createDialog, open })}
        onConfirm={handleCreateConfirm}
        initialPath={createDialog.folderPath}
        type={createDialog.type}
      />
    </div>
  );
};

  // 重命名文件或文件夹
  const renameFileOrFolder = async (oldPath: string, fileType: 'file' | 'folder') => {
    const currentName = oldPath.split('/').pop() || '';
    const newName = prompt(`重命名${fileType === 'folder' ? '文件夹' : '文件'}:`, currentName);
    
    if (!newName || newName === currentName) return;
    
    // 构建新路径
    const pathParts = oldPath.split('/');
    pathParts[pathParts.length - 1] = newName;
    const newPath = pathParts.join('/');
    
    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      gitService.renameFile(oldPath, newPath);
      
      // 如果重命名的是当前打开的文件，需要更新编辑器状态
      if (currentFile === oldPath && onFileSelect) {
        onFileSelect(newPath);
      }
      
      await loadFileTree();
    } catch (error) {
      console.error('重命名失败:', error);
      alert(`重命名失败: ${error instanceof Error ? error.message : '请重试'}`);
    }
  };

  // 删除文件或文件夹
  const deleteFileOrFolder = async (filePath: string, fileType: 'file' | 'folder') => {
    if (confirm(`确定要删除 ${filePath} 吗？`)) {
      try {
        const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
        gitService.deleteFile(filePath);
        await loadFileTree();
      } catch (error) {
        console.error('删除失败:', error);
        alert('删除失败，请重试');
      }
    }
  };

  // 打开创建文件弹窗
  const openCreateFileDialog = (type: 'file' | 'folder', folderPath?: string) => {
    setCreateDialog({
      open: true,
      type,
      folderPath
    });
  };

  // 处理创建文件/文件夹确认
  const handleCreateConfirm = async (fileName: string) => {
    const { type, folderPath } = createDialog;
    const fullPath = folderPath ? `${folderPath}/${fileName}` : fileName;
    
    try {
      const gitService = (await import('@/lib/simpleGit')).SimpleGitService.getInstance();
      
      if (type === 'file') {
        // 创建文件
        const defaultContent = fileName.endsWith('.md') 
          ? `# ${fileName.replace('.md', '')}\n\n` 
          : '';
        gitService.createFile(fullPath, defaultContent);
        
        // 自动打开新创建的文件
        if (onFileSelect) {
          onFileSelect(fullPath);
        }
      } else {
        // 创建文件夹（暂时通过创建一个隐藏文件来实现文件夹）
        gitService.createFile(`${fullPath}/.gitkeep`, '');
      }
      
      await loadFileTree();
    } catch (error) {
      console.error(`创建${type === 'folder' ? '文件夹' : '文件'}失败:`, error);
      alert(`创建${type === 'folder' ? '文件夹' : '文件'}失败，请重试`);
    }
  };

  return (
    <div className="space-y-1">
      {/* 头部工具栏 */}
      <div className="flex items-center justify-between mb-2">
        <div className="text-xs text-muted-foreground">项目文件</div>
        <div className="flex items-center space-x-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => openCreateFileDialog('file')}
              >
                <Plus className="w-3 h-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">新建文件</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={() => openCreateFileDialog('folder')}
              >
                <Folder className="w-3 h-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">新建文件夹</p>
            </TooltipContent>
          </Tooltip>
          
          <Tooltip>
            <TooltipTrigger asChild>
                              <Button
                variant="ghost"
                size="sm"
                className="h-5 w-5 p-0"
                onClick={loadFileTree}
              >
                <RotateCw className="w-3 h-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-xs">刷新</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>

      {/* 过滤选项 */}
      <div className="space-y-1 mb-2">
        <Button
          variant="ghost"
          size="sm"
          className="w-full justify-start text-xs h-5 px-2"
          onClick={() => setShowHiddenFiles(!showHiddenFiles)}
        >
          <span className="text-xs">
            {showHiddenFiles ? '隐藏' : '显示'}隐藏文件
          </span>
        </Button>
      </div>

      {/* 文件树 */}
      {loading ? (
        <div className="text-xs text-muted-foreground text-center py-4">
          加载中...
        </div>
      ) : fileTree.length === 0 ? (
        <div className="text-xs text-muted-foreground text-center py-4">
          暂无文件
        </div>
      ) : (
        <div className="space-y-0.5 max-h-96 overflow-y-auto">
          {fileTree.map(node => renderTreeNode(node))}
        </div>
      )}

      {/* 统计信息 */}
      <div className="pt-2 border-t border-border/50 mt-3">
        <div className="text-xs text-muted-foreground px-2">
          共 {fileTree.length} 个项目
        </div>
      </div>

            {/* 右键菜单 */}
      {contextMenu && (
        <div 
          className="fixed bg-popover border border-border rounded-md shadow-lg z-50 py-1"
          style={{ left: contextMenu.x, top: contextMenu.y }}
          onMouseLeave={() => setContextMenu(null)}
        >
          <button
            className="w-full text-left px-3 py-1 text-xs hover:bg-muted"
            onClick={() => {
              renameFileOrFolder(contextMenu.filePath, contextMenu.fileType);
              setContextMenu(null);
            }}
          >
            重命名
          </button>
          {contextMenu.fileType === 'folder' && (
            <>
              <button
                className="w-full text-left px-3 py-1 text-xs hover:bg-muted"
                onClick={() => {
                  openCreateFileDialog('file', contextMenu.filePath);
                  setContextMenu(null);
                }}
              >
                在此文件夹中新建文件
              </button>
              <button
                className="w-full text-left px-3 py-1 text-xs hover:bg-muted"
                onClick={() => {
                  openCreateFileDialog('folder', contextMenu.filePath);
                  setContextMenu(null);
                }}
              >
                在此文件夹中新建文件夹
              </button>
            </>
          )}
          <button
            className="w-full text-left px-3 py-1 text-xs hover:bg-muted text-destructive"
            onClick={() => {
              deleteFileOrFolder(contextMenu.filePath, contextMenu.fileType);
              setContextMenu(null);
            }}
          >
            删除
          </button>
        </div>
      )}
    </div>
  );
}



function SearchPanel() {
  return (
    <div className="space-y-3">
      <div className="text-xs text-muted-foreground mb-2">搜索</div>
      <input
        type="text"
        placeholder="在文件中搜索..."
        className="w-full text-xs px-2 py-1 bg-background border border-border rounded"
      />
      <div className="text-xs text-muted-foreground">
        未找到结果
      </div>
    </div>
  );
}