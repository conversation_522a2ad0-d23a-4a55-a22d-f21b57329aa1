# AUG 团队深入技术指导

> 下面按 **“问题 → 思路 → 代码/数据结构示例 → 性能与验收”** 的节奏回答 AUG-002 中提出的所有核心疑问。每段都标注了最小可执行步骤，方便直接拆成任务卡。

---

## 1 | 位置映射算法（TreeWalker + 偏移表）

### 1.1 总体思路

1. **渲染阶段给每个可编辑节点打上 `data-seg`**（段落或内联块对应 AST node id）。
2. **用 `document.createTreeWalker(root, NodeFilter.SHOW_TEXT)`** 线性遍历文本节点，按遇到的顺序累计 `markdownOffset`。这保证了“DOM 顺序 = Markdown 顺序”，前提是渲染器始终按 AST 顺序输出 DOM。
3. **生成两张表**：

   * `dom2md: WeakMap<Text, {start: number; end: number; seg: string}>`
   * `md2dom: Map<number, {node: Text; offset: number}>`（可二分查找定位）
4. **写入 `BoundaryIndex`**：把段落起止位缓存到现有索引，供增量 diff 使用。
5. **增量更新**：监听 `beforeinput/afterinput` 取 Range → 找到受影响段落 seg → 仅重新计算该段落后的 offset（O(Δ)）。

### 1.2 关键代码片段

```ts
export function buildMapping(root: HTMLElement) {
  const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT);
  let pos = 0;
  const dom2md = new WeakMap<Text, Mapping>();
  const md2dom: Mapping[] = [];

  while (walker.nextNode()) {
    const node = walker.currentNode as Text;
    const seg = (node.parentElement?.closest('[data-seg]') as HTMLElement).dataset.seg!;
    const len = node.data.length;

    dom2md.set(node, { start: pos, end: pos + len, seg });
    md2dom.push({ node, offset: pos });

    pos += len;
  }
  return { dom2md, md2dom };
}
```

> **嵌套列表 / 表格**：因为最外层仍按 AST 排序输出，上述遍历天然保持顺序；只要渲染器在同一段落里不乱序插 DOM，就无需额外补偿。嵌套块用不同 `data-seg` 可快速锁定层级映射。

### 1.3 边界 & 动态更新

| 场景        | 处理策略                                                                                                   |
| --------- | ------------------------------------------------------------------------------------------------------ |
| **跨段删除**  | 获取 `Range.startContainer/ endContainer`，找到起止 seg，删除 seg 间 Markdown，并重算删除段起点后的 offset；重建这部分 `dom2md` 条目 |
| **段落间插入** | 插入点恰好落在两个 seg 之间 → 直接插 AST，新增 `data-seg`；后续段落 offset 整体偏移                                              |
| **表格单元格** | 给 `<td>` 单独的 seg，内部内容当作一段处理；编辑单元格时只更新该 seg                                                             |

---

## 2 | 单人双向同步 & 冲突处理

> 单人模式下“冲突”主要指 **DOM → Markdown 同步与程序性更新** 的竞态，而非多人协作。

### 2.1 操作对象

* 采用 **“区段级别”** 最小单元（段落/inline node），可大幅减少队列长度且利于合并。
* 定义 `EditOperation` 如文档示例，额外加 `rangeLength` 便于回滚。

### 2.2 队列管理

| 规则         | 建议值                                                |
| ---------- | -------------------------------------------------- |
| **时间窗口合并** | 将 ≤ 50 ms 内连续同段落的字符输入合并                            |
| **最大长度**   | 500 条；超出时先 flush 到 Markdown，再清队列                   |
| **内存防泄漏**  | 每次 flush 后丢弃包含 `domNode.isConnected === false` 的记录 |

### 2.3 冲突检测

1. **入队前读取当前 `docVersion`**（增量解析已维护）。
2. 如果 `op.baseVersion !== currentVersion`，说明在同段落有系统级更新：

   * 先 rebase（用 diff-match-patch 计算新位移），再执行；
   * 若 rebase 失败（跨段删改），回退到源码模式弹窗提示。

---

## 3 | 长文档性能与量化指标

### 3.1 建议指标

| 文档规模   | 首屏渲染 `LCP` | 输入延迟 P95 | FPS 滚动 | 内存峰值     |
| ------ | ---------- | -------- | ------ | -------- |
| 1 K 行  | < 400 ms   | < 30 ms  | ≥ 60   | < 200 MB |
| 5 K 行  | < 600 ms   | < 60 ms  | ≥ 55   | < 350 MB |
| 10 K 行 | < 900 ms   | < 100 ms | ≥ 50   | < 450 MB |

> 你在 AUG-002 里提到 10 K 行“< 100 ms”未经定义，建议只针对 **“按键→屏幕更新”** 延迟做 P95≤100 ms 指标，更符合实际。

### 3.2 虚拟滚动集成

* 让 `SemanticScrollManager` 暴露 `visibleRange` API，实时给 `VirtualList`。
* 视窗高度 = `viewport + 2 × overScan`；`overScan` 可动态调成 0.5× 屏高，滚动越快越小。
* 可变高度元素（代码块、图片）在 mount 后 `measure()` 更新 itemSize 缓存，触发一次 soft-reflow。
* 「分段渲染」按 **段落** 为最小粒度：既易 diff，又契合现有 `BoundaryIndex`。跨段编辑时，直接合并成一个大段送增量解析处理。

### 3.3 性能测试脚本

用 Puppeteer 配合 `PerformanceObserver`：

```bash
node scripts/bench --file demo-10k.md --mode wysiwyg
```

输出 `json` 包含：`lcp, fps, inputDelayP95, heapUsed`.

---

## 4 | 阶段验收指标（四阶段）

| 阶段          | 重点                 | 必测项目 & 阈值                                                   |
| ----------- | ------------------ | ----------------------------------------------------------- |
| **P0 原型**   | 可编辑 + 双向同步         | 标题/段落/加粗/斜体；1 K 行输入延迟≤50 ms；映射准确度≥95%                       |
| **P1 完整语法** | 列表、引用、代码块、链接、图片、表格 | 覆盖率 ≥ 95%；映射≥98%；同步延迟≤80 ms                                 |
| **P2 性能强化** | 虚拟滚动 + Worker 解析   | 10 K 行指标达表 §3.1；内存<450 MB；1 h 稳定跑无泄漏                        |
| **P3 生产就绪** | 边界用例 + 自动测试        | 浏览器 ≥ Chrome 97/Edge 97/Safari 15/FF 91；E2E 通过率≥95%；覆盖率≥80% |

> 每阶段结束前跑同一脚本，对比上一步结果回归 ≤ 5%。若超标则进入 Hot-fix Sprint。

---

## 5 | 风险与降级方案

| 风险                               | 监测                                              | 降级                                     |
| -------------------------------- | ----------------------------------------------- | -------------------------------------- |
| **ContentEditable 差异** (IE 无需支持) | UA sniff + feature detection (`beforeinput` 支持) | fallback 到源码模式只读；弹 toast 提醒            |
| **性能退化** (超 200 ms)              | `requestIdleCallback` 写入 PerfLog                | 自动关闭语法高亮，暂停装饰                          |
| **数据丢失**                         | `beforeunload` 检查 `unsavedOps.length`           | IndexedDB autosave 每 10 s；Crash 恢复询问草稿 |

---

## 6 | 与现有架构集成

1. **`useAppState` 扩展**：新增 `wysiwyg` slice（cursor / editHistory / docVersion）。
2. **模式切换流程**：

   * `EditorModeProvider` 保存 `scrollTop + cursorPos` 到对应 slice；
   * 卸载前 `flushQueue()` 保证 Markdown 最新；
   * 切换后调用 `restoreCursor(scrollTop)`。误差<1 行即可。
3. **撤销/重做统一**：在顶层维护 `globalUndoStack`，不同模式 push 相同 `MarkdownPatch` 即可。

---

## 7 | 测试策略一览

| 层级     | 框架                     | 核心用例                             |
| ------ | ---------------------- | -------------------------------- |
| **单元** | Vitest                 | Mapping 函数、diff-patch、opQueue 溢出 |
| **集成** | Playwright             | 四模式切换、5/10 K 滚动、表格编辑、多浏览器兼容      |
| **性能** | Puppeteer + Lighthouse | 各文档规模指标表 §3.1                    |
| **UX** | 内部 Beta                | 5 人盲测，记录主观流畅度（Likert ≥ 4/5）      |

---

## 8 | 优先级路线图（关键路径）

1. **👍 先做 Mapping + Sync 原型**（P0）
2. Worker-based 解析 → 虚拟滚动（P1/P2 并行可视化）
3. 扩展复杂块（表格→图片→公式）
4. 自动化测试与降级机制
5. UI/UX 打磨（工具栏、快捷键）

---

### 一句话总结

**TreeWalker + 增量映射** 能最小成本接入你们现有 DOM 渲染栈；配合虚拟滚动与 Worker 解析，可在 10 K 行依旧保持 < 100 ms 输入延迟。按上表四阶段交付，即可在 \~8 周内上线高性能单人 WYSIWYG 模式。祝编码顺利!
