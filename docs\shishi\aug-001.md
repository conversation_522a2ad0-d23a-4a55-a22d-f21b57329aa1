# 实时模式（WYSIWYG）技术方案与专家咨询 - AUG-001

## 📋 项目背景

当前 Markdown 编辑器已具备三种模式：
- **源码模式**：基于 CodeMirror 6 的纯文本编辑
- **预览模式**：基于 unified/remark/rehype 的渲染预览
- **分屏模式**：源码 + 预览并排显示

现需要增加第四种模式：**实时模式（WYSIWYG）**，实现单人所见即所得编辑体验，并确保长文档下所有模式流畅运行。

## 🎯 需求分析

### 核心需求
1. **实时编辑**：在渲染后的内容上直接编辑，实时反映到 Markdown 源码
2. **双向同步**：渲染内容 ↔ Markdown 源码的实时双向同步
3. **性能保证**：长文档（10K+ 行）下保持流畅体验
4. **格式保持**：编辑过程中保持 Markdown 语法的完整性

### 技术挑战
1. **内容映射**：渲染后的 DOM 节点与 Markdown 源码位置的精确映射
2. **编辑冲突**：用户编辑与自动同步的冲突处理
3. **性能优化**：大文档的实时渲染和同步性能
4. **光标定位**：编辑过程中光标位置的准确维护

## 🏗️ 当前架构分析

### 现有渲染流程
```
Markdown 源码 → unified processor → HTML → DOM 渲染
     ↑                                        ↓
   CodeMirror                            NativeDOMRenderer
```

### 现有性能优化机制
1. **增量解析**：`IncrementalParser` - 只重新解析变化的段落
2. **自适应防抖**：`AdaptiveDebounceManager` - 根据文档复杂度动态调整防抖时间
3. **语义滚动**：`SemanticScrollManager` - 基于内容结构的滚动位置管理
4. **边界索引**：`BoundaryIndex` - 高效的文档段落边界管理

## 💡 技术方案设计

### 方案一：基于 ContentEditable 的混合架构

#### 核心思路
在现有 `NativeDOMRenderer` 基础上，增加 `contentEditable` 能力，实现直接编辑。

#### 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                    WYSIWYGEditor                            │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  EditableDOM    │  │  SourceMapper   │  │  SyncManager    │ │
│  │  (contentEdit)  │  │  (位置映射)     │  │  (双向同步)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│           现有 NativeDOMRenderer + 增量解析系统              │
└─────────────────────────────────────────────────────────────┘
```

#### 关键组件设计

1. **EditableDOM 组件**
   - 基于现有 `NativeDOMRenderer-M2-fallback`
   - 添加 `contentEditable="true"`
   - 监听 `input`、`beforeinput`、`compositionstart/end` 事件

2. **SourceMapper 映射器**
   - DOM 节点 → Markdown 源码位置的双向映射
   - 基于现有 `BoundaryIndex` 扩展
   - 支持嵌套结构（列表、引用、代码块）

3. **SyncManager 同步管理器**
   - 监听 DOM 变化，转换为 Markdown 操作
   - 防抖处理，避免频繁同步
   - 冲突检测和解决机制

### 方案二：基于 ProseMirror 的专业架构

#### 核心思路
引入 ProseMirror 作为 WYSIWYG 编辑器，与现有 CodeMirror 并行。

#### 技术架构
```
┌─────────────────────────────────────────────────────────────┐
│                    EditorArea                               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   CodeMirror    │  │   ProseMirror   │  │  NativeRenderer │ │
│  │   (源码模式)    │  │   (WYSIWYG)     │  │   (预览模式)    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│              统一的 Markdown 状态管理层                      │
└─────────────────────────────────────────────────────────────┘
```

## 🤔 关键技术疑问

### 1. 架构选择疑问
**问题**：应该选择方案一（ContentEditable）还是方案二（ProseMirror）？

**考虑因素**：
- **开发复杂度**：ContentEditable 基于现有架构，ProseMirror 需要重新设计
- **性能表现**：长文档下的渲染和编辑性能
- **功能完整性**：对复杂 Markdown 语法的支持程度
- **维护成本**：后续功能扩展和 bug 修复的难度

### 2. 位置映射算法疑问
**问题**：如何实现 DOM 节点与 Markdown 源码的精确位置映射？

**技术难点**：
- 嵌套结构的映射（列表项、引用块、代码块）
- 内联元素的映射（加粗、斜体、链接）
- 动态内容的映射（表格、数学公式）

**现有基础**：
- `BoundaryIndex` 提供段落级别的边界管理
- `IncrementalParser` 提供变化检测能力

### 3. 性能优化策略疑问
**问题**：如何在长文档下保持 WYSIWYG 模式的流畅性？

**性能瓶颈**：
- DOM 操作的频率和复杂度
- Markdown 解析和渲染的耗时
- 双向同步的计算开销

**现有优化**：
- 自适应防抖（60-400ms 动态调整）
- 增量解析（只处理变化部分）
- 虚拟滚动（语义滚动管理）

### 4. 编辑体验设计疑问
**问题**：如何处理 WYSIWYG 编辑中的特殊情况？

**特殊情况**：
- 代码块的编辑（是否需要切换到源码模式？）
- 表格的编辑（如何提供直观的编辑界面？）
- 数学公式的编辑（LaTeX 语法的处理）
- 图片和链接的编辑（如何提供便捷的编辑方式？）

### 5. 状态管理疑问
**问题**：如何设计统一的状态管理来支持四种编辑模式？

**状态同步**：
- 四种模式间的状态切换
- 撤销/重做功能的实现
- 光标位置的跨模式保持

## 📊 性能基准测试需求

### 测试场景
1. **小文档**：< 1K 行，简单格式
2. **中文档**：1K-5K 行，混合格式
3. **大文档**：5K-10K 行，复杂格式
4. **超大文档**：> 10K 行，极限测试

### 测试指标
- **首次渲染时间**：从 Markdown 到可编辑 DOM 的时间
- **编辑响应时间**：用户输入到界面更新的延迟
- **同步延迟**：WYSIWYG 编辑到源码更新的时间
- **内存占用**：长时间编辑的内存增长情况

## 🔄 开发计划建议

### 阶段一：原型验证（1-2 周）
1. 基于 ContentEditable 实现基础 WYSIWYG 编辑
2. 实现简单的 DOM ↔ Markdown 映射
3. 集成到现有 `EditorArea` 组件

### 阶段二：核心功能（2-3 周）
1. 完善位置映射算法
2. 实现双向同步机制
3. 集成现有性能优化系统

### 阶段三：性能优化（1-2 周）
1. 长文档性能测试和优化
2. 内存泄漏检测和修复
3. 用户体验细节优化

### 阶段四：功能完善（1-2 周）
1. 特殊元素编辑支持
2. 撤销/重做功能
3. 全面测试和 bug 修复

## 🙋‍♂️ 向专家请教的问题

1. **架构选择**：基于项目现状，推荐 ContentEditable 还是 ProseMirror 方案？
2. **位置映射**：有什么成熟的算法或库可以处理 DOM-Markdown 位置映射？
3. **性能优化**：长文档 WYSIWYG 编辑的最佳实践是什么？
4. **技术风险**：实现过程中可能遇到的主要技术风险和规避方案？
5. **用户体验**：WYSIWYG 模式下的交互设计建议？

## 📝 期待专家反馈

请专家针对以上方案和疑问提供：
1. 技术方案的可行性评估
2. 关键技术难点的解决思路
3. 性能优化的具体建议
4. 开发风险的预警和应对策略
5. 更好的替代方案或改进建议

---

**文档版本**：AUG-001  
**创建时间**：2025-01-04  
**作者**：AUG (项目开发人员)  
**状态**：等待专家反馈
