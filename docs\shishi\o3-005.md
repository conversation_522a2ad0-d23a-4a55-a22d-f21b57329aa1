下面给出 **“一站式实现细节手册”**——逐条拆解 AUG-005 列出的所有疑问，给出 **事件监听名单、核心 API 调用、最小可行代码片段（MVP）、边界处理** 以及与现有架构的对接要点。请直接复制到团队 Wiki，并据此拆分任务卡。为了方便检索，章节编号与 AUG-005 保持一致。

---

## 1 ContentEditable 事件总览 & 基础组件

### 1.1 推荐事件矩阵（按触发顺序）

| 事件                      | 作用                             | 必须/可选  |
| ----------------------- | ------------------------------ | ------ |
| `beforeinput`           | 拦截原生命令，识别 `inputType`          | **必须** |
| `input`                 | 兜底同步（IME 结束也会触发）               | **必须** |
| `compositionstart/​end` | 中文/韩文等 IME 合并                  | **必须** |
| `keydown/keyup`         | 快捷键（`Ctrl+B/I`, Tab/Shift-Tab） | **必须** |
| `paste`                 | 清洗粘贴内容                         | **必须** |
| `cut/copy`              | 更新剪贴板 & 内部状态                   | 可选     |
| `drop`                  | 拖拽上传图像或文件                      | 可选     |
| `blur`                  | 自动保存 & flush OpQueue           | **必须** |
| `scroll`                | 视口同步（虚拟滚动）                     | **必须** |

> **实践经验**：只要 `beforeinput` 支持完整即可捕获 95 % 的编辑场景；`input` 用作兜底，防止老浏览器缺漏。

### 1.2 最小 ContentEditable 组件示例

```tsx
// components/WYSIWYGEditor.tsx
import { useRef, useEffect } from 'react';
import { sanitizePaste, handleHotkeys } from '@/lib/editorUtils';
import { useDocStore } from '@/store/docStore';

export default function WYSIWYGEditor() {
  const ref = useRef<HTMLDivElement>(null);
  const { applyUserEdit, getMarkdown } = useDocStore();

  /* ---------- 事件绑定 ---------- */
  useEffect(() => {
    const el = ref.current!;
    /* ① beforeinput */
    el.addEventListener('beforeinput', (e: InputEvent) => {
      if (handleHotkeys(e)) return;              // Cmd+B 等
      applyUserEdit(e);                          // 写入 OpQueue
      e.preventDefault();                        // 阻止浏览器默认修改
    });

    /* ② input (兜底) */
    el.addEventListener('input', () => applyUserEdit());

    /* ③ compositionend (中文合并) */
    el.addEventListener('compositionend', () => applyUserEdit());

    /* ④ paste 清洗 */
    el.addEventListener('paste', (e: ClipboardEvent) => {
      e.preventDefault();
      document.execCommand(
        'insertHTML',
        false,
        sanitizePaste(e.clipboardData!)
      );
    });

    return () => {
      el.replaceWith(el.cloneNode(false));       // 清理监听
    };
  }, []);

  /* ---------- 渲染 ---------- */
  return (
    <div
      ref={ref}
      className="wysiwyg-editor"
      contentEditable
      suppressContentEditableWarning
      dangerouslySetInnerHTML={{ __html: getMarkdown('html') }}
    />
  );
}
```

**要点**

1. `suppressContentEditableWarning` 避免 React 重复警告。
2. 组件销毁用 `cloneNode(false)` 一行即可移除所有监听，提高热替换速度。
3. 只向外暴露一个 `applyUserEdit`，内部如何进队列与映射由 Store 负责。

---

## 2 段落映射算法（TreeWalker + 偏移表）

### 2.1 TreeWalker 模板

```ts
export function buildParagraphMap(root: HTMLElement) {
  const walker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT);
  let pos = 0;
  const map: { node: Text; start: number; end: number; seg: string }[] = [];

  while (walker.nextNode()) {
    const node = walker.currentNode as Text;
    // 最近的 data-seg 作为段落标识
    const seg = (node.parentElement!.closest('[data-seg]') as HTMLElement)
      ?.dataset.seg!;
    const len = node.data.length;

    map.push({ node, start: pos, end: pos + len, seg });
    pos += len;
  }
  return map;
}
```

**过滤策略**

* 只遍历 `Text` 节点，`NodeFilter.SHOW_TEXT` 足矣。
* 代码块整段 `data-seg="code-3"`，在映射时跳过内部换行，保证 Markdown 偏移连续。
* 空行：渲染器输出 `<p data-seg="p-5"><br></p>`，长度视为 1。

> **嵌套列表表格**：DOM 顺序即 Markdown 顺序，映射天然正确，不用深度补偿。

### 2.2 偏移量处理

| 场景           | 处理                                    |
| ------------ | ------------------------------------- |
| `&nbsp;` 等实体 | 在解析 Markdown 时转换为真实字符，映射用实际长度         |
| Emoji        | 原生 UTF-16 占 2 code units，直接算 `len`    |
| 粘贴富文本        | 先经 `sanitizePaste`→纯 Markdown，再渲染 DOM |

---

## 3 DOM ⇄ Markdown 双向同步

### 3.1 DOM → Markdown 核心步骤（单段落）

```ts
import TurndownService from 'turndown';

const td = new TurndownService({ headingStyle: 'atx' });

export const domToMarkdown = (root: HTMLElement): string =>
  td.turndown(root.innerHTML);
```

* 先按 **段落 seg** 拆分 DOM，再逐段运行 Turndown，可实现“段落级替换”。
* 列表嵌套：Turndown 内部自动处理 `ul` / `ol`，保持层级。
* 行内格式：`<strong><em>` 转成 `***text***`；如需分离，加 `td.addRule('strong-thin', …)` 自定义。

### 3.2 Markdown → DOM （增量）

1. 用 `remark-parse` 得到 `mdast`。
2. 只遍历发生变动的段落（由 OpQueue 指示），调用 `remark-rehype` + `rehype-stringify` 输出 HTML 片段。
3. `MutationObserver` 区分 **用户输入** vs **程序性 patch**：给 Patch 步骤包 `mutationFlag=true`，Observer 中检测后直接 return，避免回环。

### 3.3 冲突判定（单人模式）

* **定义**：同一段落 50 ms 内出现 2 次“用户编辑 + 程序更新”即视为冲突。
* **策略**：保留用户输入，程序 patch 延后一次防抖周期（200 ms）再尝试。若仍失败，弹 toast“格式化失败，可重试”。

---

## 4 性能优化：分段渲染 + 防抖

### 4.1 分段策略

| 规则               | 说明                            |
| ---------------- | ----------------------------- |
| 段落基准             | `500 行` ≈ 20 KB，内存 & DOM 开销平衡 |
| 视窗 = 1 屏 + 0.5 x | 高速滚动时将 OverScan 动态缩至 0.2 x    |
| 跨段编辑             | 如果 Range 覆盖 2 段，合并为 1 段重新渲染   |

```ts
// hooks/useSegmentRenderer.ts
export const useSegmentRenderer = (root: HTMLElement) => {
  const renderSegment = (segId: string) => {
    const segEl = document.querySelector(`[data-seg="${segId}"]`)!;
    // 段落级替换：先 innerHTML = '', 再插入新 HTML
  };
  return { renderSegment };
};
```

### 4.2 自适应防抖

```ts
export const adaptiveDebounce = (fn: () => void) => {
  let t: number, last = performance.now();
  const invoke = () => { fn(); last = performance.now(); };
  return () => {
    const delta = performance.now() - last;
    clearTimeout(t);
    t = window.setTimeout(invoke, Math.max(60, 200 - delta));
  };
};
```

---

## 5 与现有架构集成

### 5.1 `useAppState` 扩展

```ts
// store/docStore.ts (简化)
interface WysiwygSlice {
  cursorPos: number;
  editQueue: EditOperation[];
  flush(): void;
  restoreCursor(pos: number): void;
}

export const useDocStore = create<DocState & WysiwygSlice>((set, get) => ({
  /* 现有字段 … */
  cursorPos: 0,
  editQueue: [],
  flush: () => {/* 写回 Markdown 单例 */},
  restoreCursor: (pos) => {/* Selection API */},
}));
```

### 5.2 模式切换

```ts
const handleModeSwitch = (mode: EditorMode) => {
  const store = useDocStore.getState();
  store.flush();                            // 保证内容一致
  saveScrollAndCursor(modePrev);            // ↖️ 保存
  setEditorMode(mode);
  requestAnimationFrame(() => restoreScrollAndCursor(mode));  // ↙️ 还原
};
```

> 主线程仅负责 UI；所有解析/映射逻辑在 Store 或 Worker，避免 React 重渲染暴涨。

---

## 6 测试 & 调试

### 6.1 核心单元测试示例（Vitest）

```ts
import { buildParagraphMap } from '@/lib/paragraphMap';

it('maps offsets correctly', () => {
  document.body.innerHTML =
    '<p data-seg="p-1">Hello <strong>world</strong></p>';
  const map = buildParagraphMap(document.body);
  expect(map[0]).toMatchObject({ start: 0, end: 5 });    // "Hello"
  expect(map[1]).toMatchObject({ start: 6, end: 11 });   // "world"
});
```

### 6.2 Playwright 集成

* 场景：加载 `large.md` → 输入随机文本 → 切换四模式 → 确保 Markdown 相等。
* 断言：`expect(await page.locator('.wysiwyg-editor')).toContainText('…')`.

### 6.3 Dev 面板最小形态

```tsx
if (__DEV__) {
  window.__DOC_DEBUG__ = useDocStore.getState();    // F12 查看
}
```

---

## 7 错误处理与降级

| 触发条件                              | 动作                           |
| --------------------------------- | ---------------------------- |
| `checkWYSIWYGSupport() === false` | 直接进入源码模式 + Toast             |
| `FPS < 40` 连续 2 s                 | 关闭语法高亮 & 把 OverScan 缩至 0     |
| `flush()` 抛异常                     | IndexedDB 保存草稿 → 弹对话框“恢复/放弃” |

> **保证数据安全**：每 10 s 将 Markdown diff 写入 IndexedDB，页面 `beforeunload` 检测未 flush 的编辑，提醒保存。

---

## 8 常见浏览器差异速查

| 差异                             | Safari               | Firefox     | 解决方案                  |
| ------------------------------ | -------------------- | ----------- | --------------------- |
| `beforeinput`                  | 15+ 支持但 IME 回调顺序不同   | 92+ 支持      | 用 `compositionend` 兜底 |
| `insertParagraph` 在 `<li>` 中行为 | 插入 `<div>` 而非 `<li>` | 与 Chrome 一致 | 拦截并手动 `execCommand`   |
| Selection API 光标偏移             | SVG/图片时返回 `null`     | 正常          | 避免在可编辑 img 上设置光标      |

---

### 📌 立即行动清单

1. **把本文档同步到 Wiki → 「Implementation/05-details」**。
2. **今天完成**：搭建 `WYSIWYGEditor` MVP（上面代码），并打印映射表到控制台。
3. **本周五**：通过 *1 K 行输入延迟 < 50 ms* 测试并演示模式切换一致性。

如遇更多边界问题，随时抛给我！祝编码顺利 🚀
