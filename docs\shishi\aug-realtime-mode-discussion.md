# 实时模式（WYSIWYG）技术方案讨论

## 对话参与者
- **Augment Agent**: AI开发助手
- **专家**: 前端性能优化专家
- **用户**: 项目负责人

---

## 🎯 需求分析

**Augment Agent**: 专家您好，我需要为现有的Markdown编辑器增加第四种模式——实时模式（单人所见即所得），同时确保在长文档下所有模式都能流畅运行。

让我先介绍一下当前的技术架构：

### 当前架构概览
1. **三种现有模式**：
   - 源码模式：纯CodeMirror编辑器
   - 预览模式：纯渲染视图
   - 分屏模式：编辑器+预览并排

2. **性能优化技术**：
   - 增量解析器（IncrementalParser）
   - 自适应防抖（AdaptiveDebounceManager）
   - 语义滚动管理（SemanticScrollManager）
   - 边界索引（BoundaryIndex）

3. **渲染管道**：
   - unified + remark + rehype 处理链
   - M2优化系统（NativeDOMRenderer-M2）
   - 回退渲染器（NativeDOMRenderer-M2-fallback）

**问题**：如何在这个基础上实现真正的WYSIWYG实时编辑，同时保持长文档性能？

---

## 💭 专家分析

**专家**: 很好的问题！基于您现有的架构，我看到了一些优秀的性能优化基础。对于WYSIWYG实时模式，我们需要解决几个核心挑战：

### 核心挑战
1. **双向同步**：富文本编辑 ↔ Markdown源码
2. **实时渲染**：编辑时即时视觉反馈
3. **性能保证**：长文档下的流畅体验
4. **光标管理**：编辑位置的精确映射

### 技术方案建议

我建议采用**混合架构**，结合以下技术：

#### 1. 编辑器选择
```typescript
// 推荐使用 ProseMirror 作为WYSIWYG核心
import { EditorState, Plugin } from 'prosemirror-state';
import { EditorView } from 'prosemirror-view';
import { Schema } from 'prosemirror-model';
import { markdownParser, markdownSerializer } from 'prosemirror-markdown';
```

**理由**：
- ProseMirror 提供强大的文档模型
- 原生支持Markdown双向转换
- 可与现有CodeMirror共存
- 优秀的性能和扩展性

#### 2. 架构设计
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CodeMirror    │    │   ProseMirror   │    │   Renderer      │
│   (源码模式)     │◄──►│   (实时模式)     │◄──►│   (预览模式)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 ▼
                    ┌─────────────────────────┐
                    │    统一状态管理器        │
                    │  (Markdown Source)      │
                    └─────────────────────────┘
```

---

## 🚀 Augment Agent 方案设计

**Augment Agent**: 基于专家的建议，我设计了以下实现方案：

### 方案一：ProseMirror + 现有架构集成

#### 1. 新增实时编辑器组件
```typescript
// components/editor/RealTimeEditor.tsx
interface RealTimeEditorProps {
  content: string;
  onChange: (content: string) => void;
  onScrollPositionChange?: (position: number) => void;
  initialScrollPosition?: number;
}

export function RealTimeEditor({ content, onChange, ...props }: RealTimeEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const viewRef = useRef<EditorView | null>(null);
  
  // 集成现有的性能优化系统
  const debounceManager = useRef(new AdaptiveDebounceManager({
    minDelay: 30,  // 实时模式需要更快响应
    maxDelay: 200,
    baseDelay: 60
  }));
  
  // ProseMirror 配置
  const schema = useMemo(() => createMarkdownSchema(), []);
  const parser = useMemo(() => markdownParser(schema), [schema]);
  const serializer = useMemo(() => markdownSerializer(schema), [schema]);
}
```

#### 2. 扩展EditorArea支持四种模式
```typescript
// 在 EditorArea.tsx 中添加
type EditorMode = 'source' | 'preview' | 'split' | 'realtime';

const [editorMode, setEditorMode] = useState<EditorMode>('source');

// 新增实时模式渲染
{editorMode === 'realtime' && (
  <div className="flex-1 editor-content w-full">
    <RealTimeEditor
      content={content}
      onChange={handleContentChange}
      initialScrollPosition={initialScrollPosition?.editor}
      onScrollPositionChange={(pos) => onScrollPositionChange?.(pos, undefined)}
    />
  </div>
)}
```

#### 3. 性能优化策略

**A. 增量同步机制**
```typescript
class RealTimeSync {
  private lastMarkdown: string = '';
  private lastProseMirrorDoc: Node | null = null;
  
  // 智能双向同步
  syncToMarkdown(proseMirrorDoc: Node): string {
    // 只在文档真正变化时才序列化
    if (this.lastProseMirrorDoc?.eq(proseMirrorDoc)) {
      return this.lastMarkdown;
    }
    
    const markdown = this.serializer.serialize(proseMirrorDoc);
    this.lastMarkdown = markdown;
    this.lastProseMirrorDoc = proseMirrorDoc;
    return markdown;
  }
  
  syncFromMarkdown(markdown: string): Node {
    if (markdown === this.lastMarkdown && this.lastProseMirrorDoc) {
      return this.lastProseMirrorDoc;
    }
    
    const doc = this.parser.parse(markdown);
    this.lastMarkdown = markdown;
    this.lastProseMirrorDoc = doc;
    return doc;
  }
}
```

**B. 虚拟化长文档**
```typescript
// 利用现有的边界索引系统
class VirtualizedRealTimeEditor {
  private boundaryIndex: BoundaryIndex;
  private visibleRange: { start: number; end: number };
  
  // 只渲染可视区域的内容
  renderVisibleContent(fullContent: string): string {
    const segments = this.boundaryIndex.getVisibleSegments(this.visibleRange);
    return segments.map(seg => seg.content).join('\n');
  }
}
```

---

## 🤔 专家反馈与优化

**专家**: 您的方案很有前瞻性！我有几个优化建议：

### 建议1：渐进式实现
不要一次性实现完整的WYSIWYG，建议分阶段：

**阶段1：基础实时模式**
- 简单的富文本编辑（加粗、斜体、链接）
- 基本的Markdown双向转换
- 复用现有的性能优化系统

**阶段2：高级功能**
- 表格编辑
- 代码块语法高亮
- 数学公式支持

**阶段3：协作功能**
- 多人实时编辑
- 冲突解决

### 建议2：性能监控
```typescript
class PerformanceMonitor {
  private metrics = {
    syncTime: 0,
    renderTime: 0,
    memoryUsage: 0
  };
  
  measureSync<T>(fn: () => T): T {
    const start = performance.now();
    const result = fn();
    this.metrics.syncTime = performance.now() - start;
    return result;
  }
  
  // 自动降级机制
  shouldFallbackToSplit(): boolean {
    return this.metrics.syncTime > 100 || // 同步耗时超过100ms
           this.metrics.renderTime > 200;  // 渲染耗时超过200ms
  }
}
```

### 建议3：内存优化
```typescript
// 智能垃圾回收
class DocumentMemoryManager {
  private documentCache = new Map<string, WeakRef<any>>();
  
  // 定期清理无用的文档缓存
  cleanup() {
    for (const [key, ref] of this.documentCache) {
      if (!ref.deref()) {
        this.documentCache.delete(key);
      }
    }
  }
}
```

---

## 📋 最终实施方案

**Augment Agent**: 综合专家建议，我制定了最终的实施方案：

### 技术栈选择
- **核心编辑器**: ProseMirror
- **Markdown处理**: 现有的unified生态
- **性能优化**: 扩展现有的M2系统
- **状态管理**: 集成到现有的useAppState

### 实施步骤

#### 第一阶段：基础架构（1-2周）
1. 创建RealTimeEditor组件
2. 实现基础的ProseMirror集成
3. 添加第四种编辑模式到EditorArea
4. 基础的Markdown双向转换

#### 第二阶段：性能优化（2-3周）
1. 集成自适应防抖系统
2. 实现增量同步机制
3. 添加性能监控和自动降级
4. 长文档虚拟化支持

#### 第三阶段：功能完善（2-3周）
1. 高级Markdown元素支持
2. 工具栏和快捷键
3. 与现有Git系统集成
4. 全面测试和优化

### 风险控制
1. **渐进式发布**: 先在beta分支测试
2. **性能基准**: 建立性能测试套件
3. **回退机制**: 始终保持现有三种模式可用
4. **用户反馈**: 收集用户使用数据

---

## 🎯 下一步行动

**用户**: 方案看起来很完善！请开始实施第一阶段的开发。

**Augment Agent**: 收到！我将开始创建基础的RealTimeEditor组件和相关架构。这个方案既保持了现有系统的稳定性，又为未来的扩展留下了空间。

专家的建议非常宝贵，特别是渐进式实现和性能监控的思路，这将确保我们能够在不影响现有功能的前提下，稳步推进实时模式的开发。

---

## 📊 技术细节补充

### ProseMirror Schema 设计

**专家**: 让我详细说明一下ProseMirror的Schema设计，这是实时模式的核心：

```typescript
// lib/prosemirror-schema.ts
import { Schema } from 'prosemirror-model';
import { addListNodes } from 'prosemirror-schema-list';
import { tableNodes } from 'prosemirror-tables';

export function createMarkdownSchema(): Schema {
  const nodes = {
    doc: {
      content: 'block+'
    },
    paragraph: {
      content: 'inline*',
      group: 'block',
      parseDOM: [{ tag: 'p' }],
      toDOM: () => ['p', 0]
    },
    heading: {
      attrs: { level: { default: 1 } },
      content: 'inline*',
      group: 'block',
      defining: true,
      parseDOM: [
        { tag: 'h1', attrs: { level: 1 } },
        { tag: 'h2', attrs: { level: 2 } },
        { tag: 'h3', attrs: { level: 3 } },
        { tag: 'h4', attrs: { level: 4 } },
        { tag: 'h5', attrs: { level: 5 } },
        { tag: 'h6', attrs: { level: 6 } }
      ],
      toDOM: (node) => [`h${node.attrs.level}`, 0]
    },
    code_block: {
      content: 'text*',
      marks: '',
      group: 'block',
      code: true,
      defining: true,
      attrs: { language: { default: '' } },
      parseDOM: [{
        tag: 'pre',
        preserveWhitespace: 'full',
        getAttrs: (node) => ({
          language: (node as Element).getAttribute('data-language') || ''
        })
      }],
      toDOM: (node) => ['pre', { 'data-language': node.attrs.language }, ['code', 0]]
    },
    text: {
      group: 'inline'
    }
  };

  const marks = {
    strong: {
      parseDOM: [{ tag: 'strong' }, { tag: 'b' }],
      toDOM: () => ['strong', 0]
    },
    em: {
      parseDOM: [{ tag: 'i' }, { tag: 'em' }],
      toDOM: () => ['em', 0]
    },
    code: {
      parseDOM: [{ tag: 'code' }],
      toDOM: () => ['code', 0]
    },
    link: {
      attrs: { href: {}, title: { default: null } },
      inclusive: false,
      parseDOM: [{
        tag: 'a[href]',
        getAttrs: (node) => ({
          href: (node as Element).getAttribute('href'),
          title: (node as Element).getAttribute('title')
        })
      }],
      toDOM: (node) => ['a', { href: node.attrs.href, title: node.attrs.title }, 0]
    }
  };

  // 添加列表和表格支持
  const schemaSpec = {
    nodes: addListNodes(nodes, 'paragraph block*', 'block'),
    marks
  };

  return new Schema(schemaSpec);
}
```

### 双向同步优化

**Augment Agent**: 基于专家的Schema设计，我来实现高效的双向同步：

```typescript
// lib/realtime-sync.ts
import { Node as ProseMirrorNode } from 'prosemirror-model';
import { markdownParser, markdownSerializer } from 'prosemirror-markdown';
import { createMarkdownSchema } from './prosemirror-schema';

export class RealTimeSync {
  private schema = createMarkdownSchema();
  private parser = markdownParser(this.schema);
  private serializer = markdownSerializer(this.schema);

  private lastMarkdown: string = '';
  private lastProseMirrorDoc: ProseMirrorNode | null = null;
  private lastDocHash: string = '';

  // 快速哈希计算，用于检测文档变化
  private calculateDocHash(doc: ProseMirrorNode): string {
    return `${doc.nodeSize}-${doc.childCount}-${doc.textContent.length}`;
  }

  // 智能Markdown到ProseMirror转换
  markdownToProseMirror(markdown: string): ProseMirrorNode {
    // 如果内容没变，直接返回缓存
    if (markdown === this.lastMarkdown && this.lastProseMirrorDoc) {
      return this.lastProseMirrorDoc;
    }

    try {
      const doc = this.parser.parse(markdown);
      if (doc) {
        this.lastMarkdown = markdown;
        this.lastProseMirrorDoc = doc;
        this.lastDocHash = this.calculateDocHash(doc);
        return doc;
      }
    } catch (error) {
      console.warn('Markdown解析失败，使用回退方案:', error);
    }

    // 回退到简单文本
    return this.schema.nodes.doc.create({}, [
      this.schema.nodes.paragraph.create({},
        this.schema.text(markdown)
      )
    ]);
  }

  // 智能ProseMirror到Markdown转换
  proseMirrorToMarkdown(doc: ProseMirrorNode): string {
    const currentHash = this.calculateDocHash(doc);

    // 如果文档没变，直接返回缓存
    if (currentHash === this.lastDocHash && this.lastMarkdown) {
      return this.lastMarkdown;
    }

    try {
      const markdown = this.serializer.serialize(doc);
      this.lastMarkdown = markdown;
      this.lastProseMirrorDoc = doc;
      this.lastDocHash = currentHash;
      return markdown;
    } catch (error) {
      console.warn('ProseMirror序列化失败:', error);
      return doc.textContent; // 回退到纯文本
    }
  }

  // 清理缓存
  clearCache(): void {
    this.lastMarkdown = '';
    this.lastProseMirrorDoc = null;
    this.lastDocHash = '';
  }
}
```

### 性能监控系统

**专家**: 实时模式需要严格的性能监控，我建议这样设计：

```typescript
// lib/realtime-performance.ts
export interface PerformanceMetrics {
  syncTime: number;
  renderTime: number;
  memoryUsage: number;
  documentSize: number;
  operationCount: number;
}

export class RealTimePerformanceMonitor {
  private metrics: PerformanceMetrics = {
    syncTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    documentSize: 0,
    operationCount: 0
  };

  private performanceHistory: PerformanceMetrics[] = [];
  private maxHistorySize = 50;

  // 测量同步性能
  measureSync<T>(fn: () => T, documentSize: number): T {
    const start = performance.now();
    const result = fn();
    const syncTime = performance.now() - start;

    this.updateMetrics({
      syncTime,
      documentSize,
      operationCount: this.metrics.operationCount + 1
    });

    return result;
  }

  // 测量渲染性能
  measureRender<T>(fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const renderTime = performance.now() - start;

    this.updateMetrics({ renderTime });
    return result;
  }

  // 更新性能指标
  private updateMetrics(partial: Partial<PerformanceMetrics>): void {
    this.metrics = { ...this.metrics, ...partial };

    // 记录到历史
    this.performanceHistory.push({ ...this.metrics });
    if (this.performanceHistory.length > this.maxHistorySize) {
      this.performanceHistory.shift();
    }
  }

  // 性能评估
  getPerformanceAssessment(): {
    status: 'excellent' | 'good' | 'poor' | 'critical';
    shouldFallback: boolean;
    recommendations: string[];
  } {
    const avgSyncTime = this.getAverageMetric('syncTime');
    const avgRenderTime = this.getAverageMetric('renderTime');

    let status: 'excellent' | 'good' | 'poor' | 'critical' = 'excellent';
    let shouldFallback = false;
    const recommendations: string[] = [];

    // 同步性能评估
    if (avgSyncTime > 100) {
      status = 'critical';
      shouldFallback = true;
      recommendations.push('同步耗时过长，建议切换到分屏模式');
    } else if (avgSyncTime > 50) {
      status = 'poor';
      recommendations.push('考虑增加防抖时间或优化文档结构');
    } else if (avgSyncTime > 20) {
      status = 'good';
    }

    // 渲染性能评估
    if (avgRenderTime > 200) {
      status = 'critical';
      shouldFallback = true;
      recommendations.push('渲染耗时过长，建议使用虚拟化');
    } else if (avgRenderTime > 100) {
      status = status === 'excellent' ? 'poor' : status;
      recommendations.push('渲染性能需要优化');
    }

    return { status, shouldFallback, recommendations };
  }

  private getAverageMetric(key: keyof PerformanceMetrics): number {
    if (this.performanceHistory.length === 0) return 0;

    const sum = this.performanceHistory.reduce((acc, metrics) => acc + metrics[key], 0);
    return sum / this.performanceHistory.length;
  }
}
```

---

## 🔧 实现细节讨论

**Augment Agent**: 专家，关于工具栏和用户交互，您有什么建议？

**专家**: 实时模式的工具栏设计很关键，需要平衡功能性和简洁性：

```typescript
// components/editor/RealTimeToolbar.tsx
export interface ToolbarAction {
  id: string;
  label: string;
  icon: React.ComponentType;
  command: (view: EditorView) => boolean;
  isActive?: (state: EditorState) => boolean;
  isEnabled?: (state: EditorState) => boolean;
}

export const defaultToolbarActions: ToolbarAction[] = [
  {
    id: 'bold',
    label: '加粗',
    icon: Bold,
    command: (view) => toggleMark(view.state.schema.marks.strong)(view.state, view.dispatch),
    isActive: (state) => markActive(state, state.schema.marks.strong),
  },
  {
    id: 'italic',
    label: '斜体',
    icon: Italic,
    command: (view) => toggleMark(view.state.schema.marks.em)(view.state, view.dispatch),
    isActive: (state) => markActive(state, state.schema.marks.em),
  },
  {
    id: 'heading1',
    label: '标题1',
    icon: Heading1,
    command: (view) => setBlockType(view.state.schema.nodes.heading, { level: 1 })(view.state, view.dispatch),
    isActive: (state) => blockActive(state, state.schema.nodes.heading, { level: 1 }),
  }
  // ... 更多工具
];
```

**用户**: 这个方案很详细！我特别关心长文档的性能表现。

**专家**: 对于长文档，我们需要实现智能的内容分片和懒加载：

```typescript
// lib/document-virtualization.ts
export class DocumentVirtualizer {
  private chunkSize = 1000; // 每个分片的字符数
  private visibleChunks = new Set<number>();
  private documentChunks: Map<number, string> = new Map();

  // 将文档分片
  chunkDocument(content: string): Map<number, string> {
    const chunks = new Map<number, string>();
    const lines = content.split('\n');
    let currentChunk = '';
    let chunkIndex = 0;

    for (const line of lines) {
      if (currentChunk.length + line.length > this.chunkSize) {
        chunks.set(chunkIndex, currentChunk);
        currentChunk = line + '\n';
        chunkIndex++;
      } else {
        currentChunk += line + '\n';
      }
    }

    if (currentChunk) {
      chunks.set(chunkIndex, currentChunk);
    }

    this.documentChunks = chunks;
    return chunks;
  }

  // 获取可见区域的内容
  getVisibleContent(scrollTop: number, viewportHeight: number): string {
    // 根据滚动位置计算需要渲染的分片
    const startChunk = Math.floor(scrollTop / (viewportHeight * 0.8));
    const endChunk = startChunk + 3; // 渲染当前+前后各一个分片

    let visibleContent = '';
    for (let i = startChunk; i <= endChunk; i++) {
      const chunk = this.documentChunks.get(i);
      if (chunk) {
        visibleContent += chunk;
        this.visibleChunks.add(i);
      }
    }

    return visibleContent;
  }
}
```

---

## 📈 测试策略

**Augment Agent**: 我们需要制定全面的测试策略来确保实时模式的稳定性：

### 性能测试
```typescript
// tests/performance/realtime-performance.test.ts
describe('实时模式性能测试', () => {
  test('大文档同步性能', async () => {
    const largeDocument = generateLargeMarkdown(10000); // 10k行
    const sync = new RealTimeSync();

    const startTime = performance.now();
    const proseMirrorDoc = sync.markdownToProseMirror(largeDocument);
    const syncTime = performance.now() - startTime;

    expect(syncTime).toBeLessThan(100); // 同步时间应小于100ms
    expect(proseMirrorDoc.nodeSize).toBeGreaterThan(0);
  });

  test('频繁编辑性能', async () => {
    const editor = createRealTimeEditor();
    const operations = generateEditOperations(1000); // 1000次编辑操作

    const startTime = performance.now();
    for (const op of operations) {
      await editor.applyOperation(op);
    }
    const totalTime = performance.now() - startTime;

    expect(totalTime / operations.length).toBeLessThan(5); // 平均每次操作小于5ms
  });
});
```

### 功能测试
```typescript
// tests/functional/realtime-editor.test.ts
describe('实时编辑器功能测试', () => {
  test('Markdown双向转换准确性', () => {
    const testCases = [
      '# 标题\n\n**粗体** 和 *斜体*',
      '```javascript\nconsole.log("hello");\n```',
      '- 列表项1\n- 列表项2\n  - 嵌套项',
      '[链接](https://example.com) 和 ![图片](image.jpg)'
    ];

    const sync = new RealTimeSync();

    testCases.forEach(markdown => {
      const proseMirrorDoc = sync.markdownToProseMirror(markdown);
      const convertedMarkdown = sync.proseMirrorToMarkdown(proseMirrorDoc);
      expect(normalizeMarkdown(convertedMarkdown)).toBe(normalizeMarkdown(markdown));
    });
  });
});
```

这个全面的技术方案确保了实时模式既功能强大又性能优秀，同时与现有系统完美集成。
