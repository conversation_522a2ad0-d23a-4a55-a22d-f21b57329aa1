## 结论：**可行，但建议“ProseMirror → TipTap v3”路线**

* **单人所见即所得需求完全可实现**；关键在于 *映射精度* 与 *长文档性能*。
* 结合你们已有的增量解析、虚拟滚动等基础，**采用 ProseMirror 生态（TipTap v3 或 Milkdown）比直接改造 contentEditable 更稳妥**：

  * **成熟的 Markdown ↔ JSON 扩展**，省去自行维护 DOM-Markdown 映射的高成本。
  * 内置 **Transaction 粒度**，易做局部重算和撤销栈管理。
  * TipTap v3 刚在 2025 稳定版发布，官方引入 *Static Renderer* 与 *shouldRerenderOnTransaction=false*，明显降低大文档重绘次数([tiptap.dev][1])。
  * 社区已有 100+ 插件（表格、公式、Slash Command），大幅减少后期功能债。
* **contentEditable + 自研映射方案** 在理论上也能做，但需解决 ❶ 深层嵌套映射算法、❷ 输入法合并事件、❸ 多光标回退，风险高且维护成本大。

---

### 1 ｜ 同类产品的可借鉴做法

| 产品                               | 技术核心                | 针对长文档的技巧                                               |
| -------------------------------- | ------------------- | ------------------------------------------------------ |
| **Notion**                       | 自研块模型 + React 虚拟列表  | 每个块独立渲染 + viewport 内 diff 更新([discuss.ProseMirror][2]) |
| **Google Docs**                  | ProseMirror 变体 + OT | 后台分片 + 延迟排版                                            |
| **Hashnode / Tiptap Playground** | TipTap v3           | Transaction 去抖 & Static Renderer([tiptap.dev][1])      |
| **Liveblocks Demo**              | Lexical             | Editor.update 批量提交；DOM 分层渲染([liveblocks.io][3])        |

> 共同点：**块级虚拟化 + WebWorker 解析 + 增量装饰** —— 你们已有的 *BoundaryIndex*、*IncrementalParser* 与 *SemanticScrollManager* 与之高度契合，可直接复用。

---

### 2 ｜ 关键技术难点 & 建议

| 难点                   | 建议方案                                                                                                                                            |
| -------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------- |
| **Markdown ↔ 编辑树映射** | 使用 **`@tiptap/extension-markdown`** 或 **Milkdown mdast 适配器** 做 *Lossless Round-Trip*；复杂块（表格 / 公式）单独扩展节点 schema。                                 |
| **长文档渲染**            | 保持 **≤100 可见节点**：TipTap 的 *VirtualRenderer*；同时把 `Decoration` 计算放到 Worker，主线程仅拿 diff。参考 ProseMirror 论坛针对 100 页文档的优化讨论([discuss.ProseMirror][4])。 |
| **输入延迟**             | 主线程仅处理 `EditorView.dispatch`；在 Worker 中做 `remark` 解析，结果通过 `postMessage` 返回 JSON Patch。确保编辑 TPS ≥ 60。                                            |
| **光标与滚动同步**          | 保存 `pos` + `scrollTop` 于 `docStore`，模式切换时调用 `view.dispatch(setSelection)`；目标误差 <1 行（你们之前的滚动语义管理可直接沿用）。                                          |
| **包体积**              | 采用 **TipTap core + 按需 Extension**；配合 vite 的 *manualChunks* 与 rollup-plugin-visualizer 持续监控。                                                     |

---

### 3 ｜ 性能基准建议

| 场景          | 目标指标           | 监控方式                                       |
| ----------- | -------------- | ------------------------------------------ |
| 1 MB 文件首屏   | **< 800 ms**   | Chrome Performance `LCP`                   |
| 滚动 0 → 100% | **FPS ≥ 55**   | `requestAnimationFrame` 采样                 |
| 连续输入 50 次   | **延迟 < 16 ms** | 自研基准脚本 (`Puppeteer + PerformanceObserver`) |
| 模式切换        | **< 150 ms**   | React Profiler mark                        |

---

### 4 ｜ 开发风险与应对

| 风险                                    | 影响          | 对策                                                                                |
| ------------------------------------- | ----------- | --------------------------------------------------------------------------------- |
| **Schema 失配**（Markdown 新语法）           | 数据丢失 / 格式错乱 | 统一从 **mdast** 生成 schema，扩展前先写 *Round-Trip Test*。                                  |
| **插件冲突**（TipTap Extension 交互）         | 编辑器崩溃       | 建立 **Extension lint pipeline**，每次升级跑 E2E。                                         |
| **大文件内存泄漏**                           | 浏览器 OOM     | 开启 `--enable-precise-memory-info`，每周跑 10 min Monkey Test；定期 audit Detached Nodes。 |
| **SEO & SSR** （Next.js 13 App Router） | 预渲染失败       | 实时模式仅在 **client-side** 加载，SSR 流程走 Markdown 静态渲染。                                  |

---

### 5 ｜ 面向 AUG 的行动清单

1. **本周内**：拉出 *TipTap v3 + mdast* PoC，加载 100 KB Markdown，测输入延迟。
2. 把 **增量解析 Worker** 从现有源码模式抽象成通用服务，供实时模式复用。
3. 在 CI 加入 **性能守卫**：基准脚本回退 > 5% 自动阻断。
4. 立刻规划 **“复杂块” Extension Sprints**（表格 → 公式 → 图片）：每个块先写 Round-Trip Test，再做 UI。
5. 与产品沟通：确定 *代码块* 是否在实时模式内直接编辑，或跳转源码模式 —— 这一步最影响映射复杂度。

---

> **一句话总结**：直接在 `contentEditable` 上“硬改”会陷入映射和 IM 合并的泥潭；借助 **ProseMirror / TipTap v3** 的成熟生态与事务模型，可以把精力集中在 *性能细节* 和 *块插件*，更快、更稳达成「1 MB 长文档也丝滑」的目标。祝开发顺利!

[1]: https://tiptap.dev/blog/release-notes/tiptap-3-0-is-stable?utm_source=chatgpt.com "Tiptap 3.0 is stable – Tiptap Release Notes"
[2]: https://discuss.prosemirror.net/t/block-structure-editor-by-prosemirror/2620?utm_source=chatgpt.com "Block structure editor by prosemirror"
[3]: https://liveblocks.io/blog/which-rich-text-editor-framework-should-you-choose-in-2025?utm_source=chatgpt.com "Which rich text editor framework should you choose in 2025?"
[4]: https://discuss.prosemirror.net/t/optimizing-prosemirror-custom-style-plugin-for-large-documents/8252?utm_source=chatgpt.com "Optimizing ProseMirror Custom Style Plugin for Large ..."
